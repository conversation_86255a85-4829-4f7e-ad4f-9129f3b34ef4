<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="用户登录页面">
    <title>用户登录 - DaisyUI + Alpine.js</title>
    
    <!-- DaisyUI + Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.4.24/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js"></script>
    
    <!-- 应用数据存储 -->
    <script src="../../data/stores.js"></script>
    <script src="./auth.js"></script>
</head>
<body class="min-h-screen bg-base-200">
    <!-- 登录页面容器 -->
    <main class="min-h-screen flex items-center justify-center p-4" x-data="loginPage" x-init="init()">
        
        <!-- 登录卡片 -->
        <article class="card w-full max-w-md bg-base-100 shadow-xl">
            
            <!-- 卡片头部 -->
            <header class="card-body">
                <div class="text-center mb-6">
                    <h1 class="text-3xl font-bold text-primary">欢迎回来</h1>
                    <p class="text-base-content/60 mt-2">请登录您的账户</p>
                </div>
                
                <!-- 登录表单 -->
                <form @submit.prevent="handleLogin" class="space-y-4">
                    
                    <!-- 用户名输入 -->
                    <fieldset class="form-control w-full">
                        <label class="label" for="username">
                            <span class="label-text">用户名或邮箱</span>
                            <span class="label-text-alt text-error" 
                                  x-show="$store.forms.login.errors.username" 
                                  x-text="$store.forms.login.errors.username">
                            </span>
                        </label>
                        <input 
                            id="username"
                            type="text" 
                            placeholder="请输入用户名或邮箱" 
                            class="input input-bordered w-full"
                            :class="{ 'input-error': $store.forms.login.errors.username }"
                            x-model="$store.forms.login.username"
                            @input="clearFieldError('username')"
                            :disabled="$store.forms.login.loading"
                            required 
                            autocomplete="username" />
                    </fieldset>

                    <!-- 密码输入 -->
                    <fieldset class="form-control w-full">
                        <label class="label" for="password">
                            <span class="label-text">密码</span>
                            <span class="label-text-alt text-error" 
                                  x-show="$store.forms.login.errors.password" 
                                  x-text="$store.forms.login.errors.password">
                            </span>
                        </label>
                        <div class="relative">
                            <input 
                                id="password"
                                :type="showPassword ? 'text' : 'password'" 
                                placeholder="请输入密码" 
                                class="input input-bordered w-full pr-12"
                                :class="{ 'input-error': $store.forms.login.errors.password }"
                                x-model="$store.forms.login.password"
                                @input="clearFieldError('password')"
                                :disabled="$store.forms.login.loading"
                                required 
                                autocomplete="current-password" />
                            <button 
                                type="button"
                                class="btn btn-ghost btn-sm absolute right-1 top-1/2 transform -translate-y-1/2"
                                @click="showPassword = !showPassword"
                                :disabled="$store.forms.login.loading"
                                tabindex="-1">
                                <svg x-show="!showPassword" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                <svg x-show="showPassword" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                                </svg>
                            </button>
                        </div>
                    </fieldset>

                    <!-- 记住我和忘记密码 -->
                    <div class="flex items-center justify-between">
                        <fieldset class="form-control">
                            <label class="label cursor-pointer justify-start gap-2 p-0">
                                <input 
                                    type="checkbox" 
                                    class="checkbox checkbox-primary checkbox-sm" 
                                    x-model="$store.forms.login.remember"
                                    :disabled="$store.forms.login.loading" />
                                <span class="label-text text-sm">记住我</span>
                            </label>
                        </fieldset>
                        
                        <a href="./forgot-password.html" 
                           class="link link-primary text-sm"
                           :class="{ 'pointer-events-none opacity-50': $store.forms.login.loading }">
                            忘记密码？
                        </a>
                    </div>

                    <!-- 登录按钮 -->
                    <div class="form-control mt-6">
                        <button 
                            type="submit" 
                            class="btn btn-primary w-full"
                            :class="{ 'loading': $store.forms.login.loading }"
                            :disabled="!isFormValid || $store.forms.login.loading">
                            <span x-show="!$store.forms.login.loading">登录</span>
                            <span x-show="$store.forms.login.loading">登录中...</span>
                        </button>
                    </div>

                    <!-- 分割线 -->
                    <div class="divider">或</div>

                    <!-- 第三方登录 -->
                    <div class="space-y-2">
                        <button 
                            type="button" 
                            class="btn btn-outline w-full"
                            @click="handleSocialLogin('google')"
                            :disabled="$store.forms.login.loading">
                            <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
                                <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                            </svg>
                            使用 Google 登录
                        </button>
                        
                        <button 
                            type="button" 
                            class="btn btn-outline w-full"
                            @click="handleSocialLogin('github')"
                            :disabled="$store.forms.login.loading">
                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                            </svg>
                            使用 GitHub 登录
                        </button>
                    </div>
                </form>
            </header>
            
            <!-- 卡片底部 -->
            <footer class="card-body pt-0">
                <div class="text-center">
                    <p class="text-sm text-base-content/60">
                        还没有账户？
                        <a href="./register.html" 
                           class="link link-primary"
                           :class="{ 'pointer-events-none opacity-50': $store.forms.login.loading }">
                            立即注册
                        </a>
                    </p>
                </div>
            </footer>
        </article>
        
        <!-- 返回首页链接 -->
        <div class="absolute top-4 left-4">
            <a href="../../index.html" 
               class="btn btn-ghost btn-sm"
               :class="{ 'pointer-events-none opacity-50': $store.forms.login.loading }">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                返回首页
            </a>
        </div>
        
        <!-- 主题切换 -->
        <div class="absolute top-4 right-4">
            <div class="dropdown dropdown-end">
                <div tabindex="0" role="button" class="btn btn-ghost btn-circle btn-sm">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                    </svg>
                </div>
                <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-40">
                    <template x-for="theme in ['light', 'dark', 'cupcake', 'corporate']" :key="theme">
                        <li>
                            <a @click="$store.theme.set(theme)" 
                               :class="{ 'active': theme === $store.theme.current }"
                               x-text="theme">
                            </a>
                        </li>
                    </template>
                </ul>
            </div>
        </div>
    </main>
    
    <!-- 通知系统 -->
    <div class="toast toast-top toast-end z-50">
        <template x-for="notification in $store.notifications.items" :key="notification.id">
            <div class="alert" 
                 :class="{
                     'alert-success': notification.type === 'success',
                     'alert-error': notification.type === 'error',
                     'alert-warning': notification.type === 'warning',
                     'alert-info': notification.type === 'info'
                 }">
                <span x-text="notification.message"></span>
                <button class="btn btn-sm btn-circle btn-ghost" 
                        @click="$store.notifications.remove(notification.id)">✕</button>
            </div>
        </template>
    </div>
    
    <!-- 页面脚本 -->
    <script>
        // 登录页面逻辑
        function loginPage() {
            return {
                showPassword: false,
                
                init() {
                    // 初始化主题
                    this.$store.theme.init();
                    
                    // 清空登录表单
                    this.$store.forms.reset('login');
                    
                    // 如果已经登录，重定向到首页
                    if (this.$store.user.authenticated) {
                        this.$store.notifications.info('您已经登录，正在跳转到首页...');
                        setTimeout(() => {
                            window.location.href = '../../index.html';
                        }, 1500);
                    }
                },
                
                get isFormValid() {
                    const form = this.$store.forms.login;
                    return form.username.trim().length > 0 && 
                           form.password.trim().length > 0;
                },
                
                clearFieldError(fieldName) {
                    if (this.$store.forms.login.errors[fieldName]) {
                        delete this.$store.forms.login.errors[fieldName];
                    }
                },
                
                async handleLogin() {
                    const form = this.$store.forms.login;
                    
                    // 清除之前的错误
                    this.$store.forms.clearErrors('login');
                    
                    // 基础验证
                    const errors = {};
                    if (!form.username.trim()) {
                        errors.username = '请输入用户名或邮箱';
                    }
                    if (!form.password.trim()) {
                        errors.password = '请输入密码';
                    }
                    
                    if (Object.keys(errors).length > 0) {
                        this.$store.forms.setErrors('login', errors);
                        return;
                    }
                    
                    // 设置加载状态
                    this.$store.forms.setLoading('login', true);
                    
                    try {
                        // 调用登录 API
                        const userData = await this.$store.user.login({
                            username: form.username,
                            password: form.password
                        });
                        
                        // 登录成功，跳转到首页
                        setTimeout(() => {
                            window.location.href = '../../index.html';
                        }, 1500);
                        
                    } catch (error) {
                        // 处理登录错误
                        this.$store.notifications.error('登录失败：' + (error.message || '未知错误'));
                        
                        // 设置表单错误
                        this.$store.forms.setErrors('login', {
                            password: '用户名或密码错误'
                        });
                    } finally {
                        this.$store.forms.setLoading('login', false);
                    }
                },
                
                handleSocialLogin(provider) {
                    this.$store.notifications.info(`正在跳转到 ${provider} 登录...`);
                    
                    // 模拟第三方登录
                    setTimeout(() => {
                        this.$store.notifications.warning('第三方登录功能暂未实现');
                    }, 1000);
                }
            };
        }
    </script>
</body>
</html>
