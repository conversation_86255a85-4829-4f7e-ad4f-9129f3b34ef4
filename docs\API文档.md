# API 接口文档

## 接口设计规范

### 基础配置

#### 请求格式
- **协议**: HTTPS
- **方法**: GET, POST, PUT, DELETE
- **内容类型**: application/json
- **字符编码**: UTF-8

#### 响应格式
```javascript
// 成功响应
{
    "code": 200,
    "message": "success",
    "data": {
        // 具体数据
    },
    "timestamp": "2024-01-01T00:00:00Z"
}

// 错误响应
{
    "code": 400,
    "message": "参数错误",
    "error": {
        "field": "username",
        "reason": "用户名不能为空"
    },
    "timestamp": "2024-01-01T00:00:00Z"
}
```

#### 状态码规范
- **200**: 请求成功
- **201**: 创建成功
- **400**: 请求参数错误
- **401**: 未授权
- **403**: 禁止访问
- **404**: 资源不存在
- **500**: 服务器内部错误

### 认证机制

#### JWT Token 认证
```javascript
// 请求头
{
    "Authorization": "Bearer <token>",
    "Content-Type": "application/json"
}

// Token 结构
{
    "header": {
        "alg": "HS256",
        "typ": "JWT"
    },
    "payload": {
        "sub": "user_id",
        "iat": 1640995200,
        "exp": 1641081600,
        "role": "user"
    }
}
```

## 用户管理 API

### 用户注册
```
POST /api/auth/register
```

#### 请求参数
```javascript
{
    "username": "string",       // 用户名 (3-20字符)
    "email": "string",          // 邮箱地址
    "password": "string",       // 密码 (6-20字符)
    "confirmPassword": "string" // 确认密码
}
```

#### 响应数据
```javascript
{
    "code": 201,
    "message": "注册成功",
    "data": {
        "user": {
            "id": "uuid",
            "username": "string",
            "email": "string",
            "avatar": "string",
            "createdAt": "2024-01-01T00:00:00Z"
        },
        "token": "jwt_token"
    }
}
```

### 用户登录
```
POST /api/auth/login
```

#### 请求参数
```javascript
{
    "username": "string",       // 用户名或邮箱
    "password": "string",       // 密码
    "remember": "boolean"       // 是否记住登录
}
```

#### 响应数据
```javascript
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "user": {
            "id": "uuid",
            "username": "string",
            "email": "string",
            "avatar": "string",
            "role": "string",
            "lastLoginAt": "2024-01-01T00:00:00Z"
        },
        "token": "jwt_token",
        "expiresIn": 3600
    }
}
```

### 获取用户信息
```
GET /api/user/profile
```

#### 请求头
```javascript
{
    "Authorization": "Bearer <token>"
}
```

#### 响应数据
```javascript
{
    "code": 200,
    "message": "success",
    "data": {
        "id": "uuid",
        "username": "string",
        "email": "string",
        "avatar": "string",
        "role": "string",
        "profile": {
            "nickname": "string",
            "bio": "string",
            "location": "string",
            "website": "string"
        },
        "settings": {
            "theme": "light",
            "language": "zh-CN",
            "notifications": true
        }
    }
}
```

### 更新用户信息
```
PUT /api/user/profile
```

#### 请求参数
```javascript
{
    "nickname": "string",
    "bio": "string",
    "location": "string",
    "website": "string",
    "avatar": "string"
}
```

## 数据管理 API

### 获取数据列表
```
GET /api/data
```

#### 查询参数
```javascript
{
    "page": 1,                  // 页码
    "pageSize": 10,             // 每页条数
    "sort": "createdAt",        // 排序字段
    "order": "desc",            // 排序方向 asc/desc
    "search": "string",         // 搜索关键词
    "filter": {                 // 筛选条件
        "status": "active",
        "category": "type1"
    }
}
```

#### 响应数据
```javascript
{
    "code": 200,
    "message": "success",
    "data": {
        "items": [
            {
                "id": "uuid",
                "title": "string",
                "content": "string",
                "status": "string",
                "category": "string",
                "createdAt": "2024-01-01T00:00:00Z",
                "updatedAt": "2024-01-01T00:00:00Z"
            }
        ],
        "pagination": {
            "page": 1,
            "pageSize": 10,
            "total": 100,
            "totalPages": 10
        }
    }
}
```

### 创建数据
```
POST /api/data
```

#### 请求参数
```javascript
{
    "title": "string",          // 标题 (必填)
    "content": "string",        // 内容
    "category": "string",       // 分类
    "tags": ["string"],         // 标签数组
    "status": "draft",          // 状态: draft, published, archived
    "metadata": {               // 元数据
        "author": "string",
        "source": "string"
    }
}
```

#### 响应数据
```javascript
{
    "code": 201,
    "message": "创建成功",
    "data": {
        "id": "uuid",
        "title": "string",
        "content": "string",
        "category": "string",
        "tags": ["string"],
        "status": "draft",
        "createdAt": "2024-01-01T00:00:00Z"
    }
}
```

### 更新数据
```
PUT /api/data/:id
```

#### 路径参数
- `id`: 数据ID

#### 请求参数
```javascript
{
    "title": "string",
    "content": "string",
    "category": "string",
    "tags": ["string"],
    "status": "string"
}
```

### 删除数据
```
DELETE /api/data/:id
```

#### 路径参数
- `id`: 数据ID

#### 响应数据
```javascript
{
    "code": 200,
    "message": "删除成功"
}
```

## 文件上传 API

### 上传文件
```
POST /api/upload
```

#### 请求格式
- Content-Type: multipart/form-data

#### 请求参数
```javascript
{
    "file": "File",             // 文件对象
    "type": "image",            // 文件类型: image, document, video
    "folder": "avatars"         // 存储文件夹
}
```

#### 响应数据
```javascript
{
    "code": 200,
    "message": "上传成功",
    "data": {
        "id": "uuid",
        "filename": "string",
        "originalName": "string",
        "size": 1024,
        "mimeType": "image/jpeg",
        "url": "https://cdn.example.com/file.jpg",
        "thumbnail": "https://cdn.example.com/thumb.jpg"
    }
}
```

### 获取文件信息
```
GET /api/upload/:id
```

#### 路径参数
- `id`: 文件ID

#### 响应数据
```javascript
{
    "code": 200,
    "message": "success",
    "data": {
        "id": "uuid",
        "filename": "string",
        "originalName": "string",
        "size": 1024,
        "mimeType": "string",
        "url": "string",
        "uploadedAt": "2024-01-01T00:00:00Z"
    }
}
```

## 系统配置 API

### 获取系统配置
```
GET /api/config
```

#### 响应数据
```javascript
{
    "code": 200,
    "message": "success",
    "data": {
        "app": {
            "name": "应用名称",
            "version": "1.0.0",
            "description": "应用描述"
        },
        "features": {
            "registration": true,
            "fileUpload": true,
            "notifications": true
        },
        "limits": {
            "maxFileSize": 10485760,    // 10MB
            "maxFiles": 5,
            "rateLimit": 100
        }
    }
}
```

## 错误处理

### 错误码定义
```javascript
const ERROR_CODES = {
    // 通用错误 1000-1999
    INVALID_PARAMS: 1001,       // 参数错误
    MISSING_REQUIRED: 1002,     // 缺少必填参数
    INVALID_FORMAT: 1003,       // 格式错误
    
    // 认证错误 2000-2999
    UNAUTHORIZED: 2001,         // 未授权
    TOKEN_EXPIRED: 2002,        // Token过期
    INVALID_TOKEN: 2003,        // 无效Token
    
    // 业务错误 3000-3999
    USER_NOT_FOUND: 3001,       // 用户不存在
    EMAIL_EXISTS: 3002,         // 邮箱已存在
    WRONG_PASSWORD: 3003,       // 密码错误
    
    // 系统错误 5000-5999
    SERVER_ERROR: 5001,         // 服务器错误
    DATABASE_ERROR: 5002,       // 数据库错误
    NETWORK_ERROR: 5003         // 网络错误
};
```

### 错误响应示例
```javascript
// 参数验证错误
{
    "code": 1001,
    "message": "参数验证失败",
    "errors": [
        {
            "field": "email",
            "message": "邮箱格式不正确"
        },
        {
            "field": "password",
            "message": "密码长度至少6位"
        }
    ]
}

// 业务逻辑错误
{
    "code": 3002,
    "message": "邮箱已被注册",
    "data": {
        "field": "email",
        "value": "<EMAIL>"
    }
}
```

## 接口测试

### 测试环境
- **开发环境**: https://dev-api.example.com
- **测试环境**: https://test-api.example.com
- **生产环境**: https://api.example.com

### 测试工具
- **Postman**: 接口调试工具
- **curl**: 命令行测试
- **Jest**: 自动化测试框架

### 测试用例示例
```javascript
// 用户注册测试
describe('用户注册 API', () => {
    test('正常注册', async () => {
        const response = await api.post('/auth/register', {
            username: 'testuser',
            email: '<EMAIL>',
            password: '123456',
            confirmPassword: '123456'
        });
        
        expect(response.status).toBe(201);
        expect(response.data.code).toBe(201);
        expect(response.data.data.user.username).toBe('testuser');
    });
    
    test('邮箱重复注册', async () => {
        const response = await api.post('/auth/register', {
            username: 'testuser2',
            email: '<EMAIL>',
            password: '123456',
            confirmPassword: '123456'
        });
        
        expect(response.status).toBe(400);
        expect(response.data.code).toBe(3002);
    });
});
```
