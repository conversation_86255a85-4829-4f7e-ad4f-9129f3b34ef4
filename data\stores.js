// Alpine.js 全局状态存储
document.addEventListener('alpine:init', () => {
    
    // 应用核心状态
    Alpine.store('app', {
        brandName: 'DaisyUI + Alpine.js',
        version: '1.0.0',
        initialized: false,
        loading: false,
        error: null,
        
        init() {
            console.log('应用初始化中...');
            this.initialized = true;
            console.log('应用初始化完成');
        },
        
        setLoading(status) {
            this.loading = status;
        },
        
        setError(error) {
            this.error = error;
            if (error) {
                Alpine.store('notifications').add({
                    type: 'error',
                    message: error
                });
            }
        }
    });
    
    // 主题管理状态
    Alpine.store('theme', {
        available: [
            'light', 'dark', 'cupcake', 'bumblebee', 'emerald', 
            'corporate', 'synthwave', 'retro', 'cyberpunk', 'valentine',
            'halloween', 'garden', 'forest', 'aqua', 'lofi',
            'pastel', 'fantasy', 'wireframe', 'black', 'luxury',
            'dracula', 'cmyk', 'autumn', 'business', 'acid',
            'lemonade', 'night', 'coffee', 'winter', 'dim'
        ],
        current: 'light',
        
        set(themeName) {
            if (this.available.includes(themeName)) {
                document.documentElement.setAttribute('data-theme', themeName);
                this.current = themeName;
                localStorage.setItem('theme', themeName);
                
                Alpine.store('notifications').add({
                    type: 'success',
                    message: `主题已切换为：${themeName}`
                });
            }
        },
        
        init() {
            const saved = localStorage.getItem('theme') || 'light';
            this.set(saved);
        },
        
        toggle() {
            const newTheme = this.current === 'light' ? 'dark' : 'light';
            this.set(newTheme);
        }
    });
    
    // 用户状态管理
    Alpine.store('user', {
        current: null,
        authenticated: false,
        
        init() {
            // 从本地存储加载用户信息
            const savedUser = localStorage.getItem('user');
            if (savedUser) {
                try {
                    const userData = JSON.parse(savedUser);
                    this.setUser(userData);
                } catch (error) {
                    console.error('加载用户数据失败:', error);
                    localStorage.removeItem('user');
                }
            }
        },
        
        setUser(userData) {
            this.current = userData;
            this.authenticated = true;
            localStorage.setItem('user', JSON.stringify(userData));
            
            Alpine.store('notifications').add({
                type: 'success',
                message: `欢迎回来，${userData.name}！`
            });
        },
        
        login(credentials) {
            // 模拟登录逻辑
            return new Promise((resolve) => {
                setTimeout(() => {
                    const userData = {
                        id: 1,
                        name: credentials.username || '用户',
                        email: credentials.email || '<EMAIL>',
                        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=' + (credentials.username || 'user'),
                        role: 'user'
                    };
                    this.setUser(userData);
                    resolve(userData);
                }, 1000);
            });
        },
        
        logout() {
            const userName = this.current?.name || '用户';
            this.current = null;
            this.authenticated = false;
            localStorage.removeItem('user');
            
            Alpine.store('notifications').add({
                type: 'info',
                message: `再见，${userName}！`
            });
        },
        
        updateProfile(profileData) {
            if (this.current) {
                this.current = { ...this.current, ...profileData };
                localStorage.setItem('user', JSON.stringify(this.current));
                
                Alpine.store('notifications').add({
                    type: 'success',
                    message: '个人资料已更新'
                });
            }
        }
    });
    
    // 导航状态管理
    Alpine.store('navigation', {
        currentPage: 'home',
        drawerOpen: false,
        activeTopItem: 1,
        activeSideItem: 1,
        breadcrumbs: [],
        
        // 顶部菜单项
        topMenuItems: [
            { id: 1, label: '首页', href: '/' },
            { id: 2, label: '产品', href: '/products' },
            { id: 3, label: '服务', href: '/services' },
            { id: 4, label: '关于', href: '/about' }
        ],
        
        // 侧边栏菜单项
        sideMenuItems: [
            { 
                id: 1, 
                label: '仪表板', 
                href: '/dashboard',
                icon: 'M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z'
            },
            { 
                id: 2, 
                label: '用户管理', 
                href: '/users',
                icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
            },
            { 
                id: 3, 
                label: '产品管理', 
                href: '/products',
                icon: 'M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4'
            },
            { 
                id: 4, 
                label: '订单管理', 
                href: '/orders',
                icon: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01'
            },
            { 
                id: 5, 
                label: '报表分析', 
                href: '/analytics',
                icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'
            },
            { 
                id: 6, 
                label: '系统设置', 
                href: '/settings',
                icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z'
            }
        ],
        
        init() {
            // 初始化导航状态
            this.updateBreadcrumbs();
        },
        
        setPage(page) {
            this.currentPage = page;
            this.updateBreadcrumbs();
            
            // 移动端自动关闭抽屉
            if (window.innerWidth < 1024) {
                this.drawerOpen = false;
            }
        },
        
        setActiveTopItem(itemId) {
            this.activeTopItem = itemId;
            const item = this.topMenuItems.find(i => i.id === itemId);
            if (item) {
                this.setPage(item.href.substring(1) || 'home');
            }
        },
        
        setActiveSideItem(itemId) {
            this.activeSideItem = itemId;
            const item = this.sideMenuItems.find(i => i.id === itemId);
            if (item) {
                this.setPage(item.href.substring(1) || 'home');
            }
        },
        
        toggleDrawer() {
            this.drawerOpen = !this.drawerOpen;
        },
        
        updateBreadcrumbs() {
            // 根据当前页面更新面包屑
            const breadcrumbs = [
                { id: 1, label: '首页', href: '/' }
            ];
            
            if (this.currentPage !== 'home') {
                const pageLabels = {
                    'dashboard': '仪表板',
                    'users': '用户管理',
                    'products': '产品管理',
                    'orders': '订单管理',
                    'analytics': '报表分析',
                    'settings': '系统设置'
                };
                
                if (pageLabels[this.currentPage]) {
                    breadcrumbs.push({
                        id: 2,
                        label: pageLabels[this.currentPage],
                        href: `/${this.currentPage}`
                    });
                }
            }
            
            this.breadcrumbs = breadcrumbs;
        }
    });
    
    // 通知系统状态
    Alpine.store('notifications', {
        items: [],
        maxItems: 5,
        
        add(notification) {
            const id = Date.now() + Math.random();
            const newNotification = {
                id,
                type: 'info',
                message: '',
                duration: 5000,
                autoRemove: true,
                ...notification
            };
            
            this.items.unshift(newNotification);
            
            // 限制通知数量
            if (this.items.length > this.maxItems) {
                this.items = this.items.slice(0, this.maxItems);
            }
            
            // 自动移除
            if (newNotification.autoRemove && newNotification.duration > 0) {
                setTimeout(() => {
                    this.remove(id);
                }, newNotification.duration);
            }
            
            return id;
        },
        
        remove(id) {
            this.items = this.items.filter(item => item.id !== id);
        },
        
        clear() {
            this.items = [];
        },
        
        // 便捷方法
        success(message, options = {}) {
            return this.add({ type: 'success', message, ...options });
        },
        
        error(message, options = {}) {
            return this.add({ type: 'error', message, autoRemove: false, ...options });
        },
        
        warning(message, options = {}) {
            return this.add({ type: 'warning', message, ...options });
        },
        
        info(message, options = {}) {
            return this.add({ type: 'info', message, ...options });
        }
    });
    
    // 表单状态管理
    Alpine.store('forms', {
        // 登录表单
        login: {
            username: '',
            password: '',
            remember: false,
            loading: false,
            errors: {}
        },
        
        // 注册表单
        register: {
            username: '',
            email: '',
            password: '',
            confirmPassword: '',
            agreeTerms: false,
            loading: false,
            errors: {}
        },
        
        // 用户资料表单
        profile: {
            name: '',
            email: '',
            bio: '',
            avatar: '',
            loading: false,
            errors: {}
        },
        
        // 重置表单
        reset(formName) {
            if (this[formName]) {
                Object.keys(this[formName]).forEach(key => {
                    if (typeof this[formName][key] === 'string') {
                        this[formName][key] = '';
                    } else if (typeof this[formName][key] === 'boolean') {
                        this[formName][key] = false;
                    } else if (typeof this[formName][key] === 'object') {
                        this[formName][key] = {};
                    }
                });
            }
        },
        
        // 设置加载状态
        setLoading(formName, loading) {
            if (this[formName]) {
                this[formName].loading = loading;
            }
        },
        
        // 设置错误
        setErrors(formName, errors) {
            if (this[formName]) {
                this[formName].errors = errors;
            }
        },
        
        // 清除错误
        clearErrors(formName) {
            if (this[formName]) {
                this[formName].errors = {};
            }
        }
    });
});
