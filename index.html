<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="基于 DaisyUI 和 Alpine.js 的语义化组件项目">
    <meta name="keywords" content="DaisyUI, Alpine.js, 语义化, 组件化">
    <title>DaisyUI + Alpine.js 语义化项目</title>
    
    <!-- DaisyUI + Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.4.24/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js"></script>
    
    <!-- 应用数据存储 -->
    <script src="./data/stores.js"></script>
</head>
<body class="min-h-screen bg-base-100">
    <!-- 主应用容器 -->
    <div id="app" x-data="app" x-init="init()">
        
        <!-- 抽屉式布局 -->
        <div class="drawer lg:drawer-open">
            <!-- 抽屉切换 -->
            <input id="drawer-toggle" type="checkbox" class="drawer-toggle" x-model="$store.navigation.drawerOpen" />
            
            <!-- 主内容区域 -->
            <main class="drawer-content flex flex-col">
                
                <!-- 顶部导航栏 -->
                <header class="navbar bg-base-100 shadow-sm border-b border-base-200">
                    <nav class="navbar-start">
                        <!-- 移动端菜单按钮 -->
                        <label for="drawer-toggle" class="btn btn-square btn-ghost lg:hidden">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </label>
                        
                        <!-- 品牌标识 -->
                        <a class="btn btn-ghost text-xl font-bold" href="/">
                            <span x-text="$store.app.brandName"></span>
                        </a>
                    </nav>
                    
                    <!-- 导航中心 -->
                    <section class="navbar-center hidden lg:flex">
                        <ul class="menu menu-horizontal px-1">
                            <template x-for="item in $store.navigation.topMenuItems" :key="item.id">
                                <li>
                                    <a :href="item.href" 
                                       :class="{ 'active': item.id === $store.navigation.activeTopItem }"
                                       @click="$store.navigation.setActiveTopItem(item.id)"
                                       x-text="item.label">
                                    </a>
                                </li>
                            </template>
                        </ul>
                    </section>
                    
                    <!-- 导航右侧 -->
                    <aside class="navbar-end gap-2">
                        <!-- 主题切换 -->
                        <div class="dropdown dropdown-end">
                            <div tabindex="0" role="button" class="btn btn-ghost btn-circle">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                                </svg>
                            </div>
                            <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                                <template x-for="theme in $store.theme.available" :key="theme">
                                    <li>
                                        <a @click="$store.theme.set(theme)" 
                                           :class="{ 'active': theme === $store.theme.current }"
                                           x-text="theme">
                                        </a>
                                    </li>
                                </template>
                            </ul>
                        </div>
                        
                        <!-- 用户菜单 -->
                        <div class="dropdown dropdown-end" x-show="$store.user.authenticated">
                            <div tabindex="0" role="button" class="btn btn-ghost btn-circle avatar">
                                <div class="w-10 rounded-full">
                                    <img :src="$store.user.current?.avatar || '/assets/images/default-avatar.png'" 
                                         :alt="$store.user.current?.name" />
                                </div>
                            </div>
                            <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                                <li><a href="/profile">个人资料</a></li>
                                <li><a href="/settings">设置</a></li>
                                <li><hr class="my-1"></li>
                                <li><a @click="$store.user.logout()">退出登录</a></li>
                            </ul>
                        </div>
                        
                        <!-- 登录按钮 -->
                        <a class="btn btn-primary" href="/login" x-show="!$store.user.authenticated">
                            登录
                        </a>
                    </aside>
                </header>
                
                <!-- 面包屑导航 -->
                <nav class="breadcrumbs text-sm px-4 py-2 bg-base-200" x-show="$store.navigation.breadcrumbs.length > 0">
                    <ul>
                        <template x-for="crumb in $store.navigation.breadcrumbs" :key="crumb.id">
                            <li>
                                <a :href="crumb.href" x-text="crumb.label"></a>
                            </li>
                        </template>
                    </ul>
                </nav>
                
                <!-- 页面内容区域 -->
                <section class="flex-1 p-4">
                    <!-- 英雄区块 -->
                    <div class="hero min-h-96 bg-base-200 rounded-lg mb-8">
                        <div class="hero-content text-center">
                            <div class="max-w-md">
                                <h1 class="text-5xl font-bold">Hello there</h1>
                                <p class="py-6">
                                    这是一个完全基于 DaisyUI 原生组件的语义化项目示例。
                                    使用 Alpine.js 进行数据绑定和状态管理。
                                </p>
                                <button class="btn btn-primary" @click="$store.navigation.setPage('dashboard')">
                                    开始使用
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 功能卡片 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                        <template x-for="feature in features" :key="feature.id">
                            <article class="card bg-base-100 shadow-xl">
                                <figure class="px-10 pt-10">
                                    <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center">
                                        <svg class="w-8 h-8 text-primary-content" fill="currentColor" viewBox="0 0 20 20">
                                            <path :d="feature.icon"></path>
                                        </svg>
                                    </div>
                                </figure>
                                <div class="card-body items-center text-center">
                                    <h2 class="card-title" x-text="feature.title"></h2>
                                    <p x-text="feature.description"></p>
                                    <div class="card-actions">
                                        <button class="btn btn-primary btn-sm" @click="learnMore(feature.id)">
                                            了解更多
                                        </button>
                                    </div>
                                </div>
                            </article>
                        </template>
                    </div>
                    
                    <!-- 统计数据 -->
                    <div class="stats shadow w-full mb-8">
                        <template x-for="stat in stats" :key="stat.id">
                            <div class="stat">
                                <div class="stat-figure text-primary">
                                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                        <path :d="stat.icon"></path>
                                    </svg>
                                </div>
                                <div class="stat-title" x-text="stat.title"></div>
                                <div class="stat-value text-primary" x-text="stat.value"></div>
                                <div class="stat-desc" x-text="stat.description"></div>
                            </div>
                        </template>
                    </div>
                </section>
                
                <!-- 页脚 -->
                <footer class="footer footer-center p-10 bg-base-200 text-base-content rounded">
                    <nav class="grid grid-flow-col gap-4">
                        <a class="link link-hover" href="/about">关于我们</a>
                        <a class="link link-hover" href="/contact">联系我们</a>
                        <a class="link link-hover" href="/privacy">隐私政策</a>
                        <a class="link link-hover" href="/terms">服务条款</a>
                    </nav>
                    <nav>
                        <div class="grid grid-flow-col gap-4">
                            <a class="link" href="#" aria-label="Twitter">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                                </svg>
                            </a>
                            <a class="link" href="#" aria-label="GitHub">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                </svg>
                            </a>
                        </div>
                    </nav>
                    <aside>
                        <p>Copyright © 2024 - All right reserved by ACME Industries Ltd</p>
                    </aside>
                </footer>
            </main>
            
            <!-- 侧边栏 -->
            <aside class="drawer-side">
                <label for="drawer-toggle" aria-label="close sidebar" class="drawer-overlay"></label>
                <nav class="menu p-4 w-80 min-h-full bg-base-200 text-base-content">
                    <!-- 侧边栏标题 -->
                    <header class="mb-4">
                        <h2 class="text-lg font-semibold px-4">导航菜单</h2>
                    </header>
                    
                    <!-- 菜单项 -->
                    <ul>
                        <template x-for="item in $store.navigation.sideMenuItems" :key="item.id">
                            <li>
                                <a :href="item.href" 
                                   :class="{ 'active': item.id === $store.navigation.activeSideItem }"
                                   @click="$store.navigation.setActiveSideItem(item.id)">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                        <path :d="item.icon"></path>
                                    </svg>
                                    <span x-text="item.label"></span>
                                </a>
                            </li>
                        </template>
                    </ul>
                </nav>
            </aside>
        </div>
        
        <!-- 通知系统 -->
        <div class="toast toast-top toast-end z-50">
            <template x-for="notification in $store.notifications.items" :key="notification.id">
                <div class="alert" 
                     :class="{
                         'alert-success': notification.type === 'success',
                         'alert-error': notification.type === 'error',
                         'alert-warning': notification.type === 'warning',
                         'alert-info': notification.type === 'info'
                     }">
                    <span x-text="notification.message"></span>
                    <button class="btn btn-sm btn-circle btn-ghost" 
                            @click="$store.notifications.remove(notification.id)">✕</button>
                </div>
            </template>
        </div>
    </div>
    
    <!-- 应用逻辑 -->
    <script>
        // 主应用数据
        function app() {
            return {
                features: [
                    {
                        id: 1,
                        title: '语义化组件',
                        description: '使用语义化的 HTML 标签和 DaisyUI 组件',
                        icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
                    },
                    {
                        id: 2,
                        title: '零自定义样式',
                        description: '完全依赖 DaisyUI 原生组件和 Tailwind 工具类',
                        icon: 'M13 10V3L4 14h7v7l9-11h-7z'
                    },
                    {
                        id: 3,
                        title: '模块化设计',
                        description: '按功能模块组织代码和组件',
                        icon: 'M19 11H5m14-7H5m14 14H5'
                    }
                ],
                
                stats: [
                    {
                        id: 1,
                        title: '组件数量',
                        value: '50+',
                        description: 'DaisyUI 原生组件',
                        icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
                    },
                    {
                        id: 2,
                        title: '主题支持',
                        value: '30+',
                        description: '内置主题选择',
                        icon: 'M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z'
                    },
                    {
                        id: 3,
                        title: '响应式',
                        value: '100%',
                        description: '完全响应式设计',
                        icon: 'M13 10V3L4 14h7v7l9-11h-7z'
                    }
                ],
                
                init() {
                    // 初始化应用
                    this.$store.app.init();
                    this.$store.theme.init();
                    this.$store.user.init();
                    this.$store.navigation.init();
                    
                    // 显示欢迎消息
                    this.$store.notifications.add({
                        type: 'success',
                        message: '欢迎使用 DaisyUI + Alpine.js 语义化项目！'
                    });
                },
                
                learnMore(featureId) {
                    const feature = this.features.find(f => f.id === featureId);
                    this.$store.notifications.add({
                        type: 'info',
                        message: `了解更多关于：${feature.title}`
                    });
                }
            };
        }
    </script>
</body>
</html>
