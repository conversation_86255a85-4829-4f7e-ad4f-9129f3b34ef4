# DaisyUI 语义化组件使用文档

## 组件使用指南

### 设计原则

1. **语义化优先**: 使用语义化的 HTML 标签结合 DaisyUI 类
2. **零自定义样式**: 完全使用 DaisyUI 原生组件和 Tailwind 工具类
3. **数据驱动**: 使用 Alpine.js 进行数据绑定和状态管理
4. **模块化组织**: 按功能模块组织组件和逻辑
5. **响应式设计**: 利用 DaisyUI 内置的响应式特性

### 命名规范

- HTML 语义化标签: `<main>`, `<section>`, `<article>`, `<header>`, `<nav>`, `<aside>`, `<footer>`
- DaisyUI 类名: 严格按照官方文档使用，如 `btn`, `modal`, `form-control`
- Alpine.js 数据: 使用描述性命名，如 `x-data="userProfile"`, `x-data="navigationMenu"`

## 布局组件

### Navbar 导航栏

#### 语义化结构
```html
<nav class="navbar bg-base-100" x-data="navigationMenu">
  <header class="navbar-start">
    <div class="dropdown">
      <div tabindex="0" role="button" class="btn btn-ghost lg:hidden">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h8m-8 6h16"></path>
        </svg>
      </div>
      <ul tabindex="0" class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
        <template x-for="item in mobileMenuItems" :key="item.id">
          <li><a :href="item.href" x-text="item.label"></a></li>
        </template>
      </ul>
    </div>
    <a class="btn btn-ghost text-xl" x-text="brandName"></a>
  </header>

  <section class="navbar-center hidden lg:flex">
    <ul class="menu menu-horizontal px-1">
      <template x-for="item in menuItems" :key="item.id">
        <li><a :href="item.href" x-text="item.label"></a></li>
      </template>
    </ul>
  </section>

  <aside class="navbar-end">
    <button class="btn btn-ghost btn-circle" @click="toggleTheme">
      <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
        <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
      </svg>
    </button>
  </aside>
</nav>
```

#### Alpine.js 数据绑定
```javascript
Alpine.data('navigationMenu', () => ({
    brandName: '我的应用',
    menuItems: [
        { id: 1, label: '首页', href: '/' },
        { id: 2, label: '产品', href: '/products' },
        { id: 3, label: '关于', href: '/about' }
    ],
    mobileMenuItems: [
        { id: 1, label: '首页', href: '/' },
        { id: 2, label: '产品', href: '/products' },
        { id: 3, label: '关于', href: '/about' },
        { id: 4, label: '联系', href: '/contact' }
    ],

    toggleTheme() {
        this.$store.theme.current === 'light'
            ? this.$store.theme.set('dark')
            : this.$store.theme.set('light');
    }
}));
```

### Drawer 抽屉式侧边栏

#### 语义化结构
```html
<aside class="drawer" x-data="sidebarDrawer">
  <input id="drawer-toggle" type="checkbox" class="drawer-toggle" x-model="isOpen" />

  <main class="drawer-content flex flex-col">
    <!-- 页面内容 -->
    <header class="navbar w-full">
      <div class="flex-none lg:hidden">
        <label for="drawer-toggle" class="btn btn-square btn-ghost">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </label>
      </div>
      <div class="flex-1 px-2 mx-2" x-text="pageTitle"></div>
    </header>

    <section class="flex-1 p-4">
      <!-- 主要内容区域 -->
      <slot></slot>
    </section>
  </main>

  <nav class="drawer-side">
    <label for="drawer-toggle" aria-label="close sidebar" class="drawer-overlay"></label>
    <aside class="menu p-4 w-80 min-h-full bg-base-200 text-base-content">
      <header class="mb-4">
        <h2 class="text-lg font-semibold" x-text="sidebarTitle"></h2>
      </header>

      <ul>
        <template x-for="item in menuItems" :key="item.id">
          <li>
            <a :href="item.href"
               :class="{ 'active': item.id === activeItem }"
               @click="setActiveItem(item.id)">
              <i :class="item.icon" x-show="item.icon"></i>
              <span x-text="item.label"></span>
            </a>
          </li>
        </template>
      </ul>
    </aside>
  </nav>
</aside>
```

#### Alpine.js 数据绑定
```javascript
Alpine.data('sidebarDrawer', () => ({
    isOpen: false,
    pageTitle: '仪表板',
    sidebarTitle: '导航菜单',
    activeItem: 1,
    menuItems: [
        { id: 1, label: '仪表板', href: '/dashboard', icon: 'icon-dashboard' },
        { id: 2, label: '用户管理', href: '/users', icon: 'icon-users' },
        { id: 3, label: '设置', href: '/settings', icon: 'icon-settings' }
    ],

    setActiveItem(itemId) {
        this.activeItem = itemId;
        this.isOpen = false; // 移动端选择后关闭
    }
}));
```

## 表单组件

### Form 表单

#### 语义化结构
```html
<form class="space-y-4" x-data="userForm" @submit.prevent="submitForm">
  <fieldset class="form-control w-full max-w-xs">
    <label class="label" for="username">
      <span class="label-text">用户名</span>
      <span class="label-text-alt text-error" x-show="errors.username" x-text="errors.username"></span>
    </label>
    <input
      id="username"
      type="text"
      placeholder="请输入用户名"
      class="input input-bordered w-full max-w-xs"
      :class="{ 'input-error': errors.username }"
      x-model="form.username"
      @blur="validateField('username')"
      required />
  </fieldset>

  <fieldset class="form-control w-full max-w-xs">
    <label class="label" for="email">
      <span class="label-text">邮箱地址</span>
      <span class="label-text-alt text-error" x-show="errors.email" x-text="errors.email"></span>
    </label>
    <input
      id="email"
      type="email"
      placeholder="请输入邮箱地址"
      class="input input-bordered w-full max-w-xs"
      :class="{ 'input-error': errors.email }"
      x-model="form.email"
      @blur="validateField('email')"
      required />
  </fieldset>

  <fieldset class="form-control w-full max-w-xs">
    <label class="label" for="role">
      <span class="label-text">用户角色</span>
    </label>
    <select
      id="role"
      class="select select-bordered w-full max-w-xs"
      x-model="form.role">
      <option disabled selected>请选择角色</option>
      <template x-for="role in availableRoles" :key="role.value">
        <option :value="role.value" x-text="role.label"></option>
      </template>
    </select>
  </fieldset>

  <fieldset class="form-control">
    <label class="label cursor-pointer justify-start gap-2">
      <input
        type="checkbox"
        class="checkbox checkbox-primary"
        x-model="form.agreeTerms" />
      <span class="label-text">我同意服务条款</span>
    </label>
  </fieldset>

  <div class="form-control mt-6">
    <button
      type="submit"
      class="btn btn-primary"
      :class="{ 'loading': isSubmitting }"
      :disabled="!isFormValid || isSubmitting">
      <span x-show="!isSubmitting">提交</span>
      <span x-show="isSubmitting">提交中...</span>
    </button>
  </div>
</form>
```

## 复合组件

### Modal 组件

#### 功能描述
模态对话框组件，支持自定义内容和操作。

#### 属性配置
```javascript
{
    isOpen: false,              // 是否打开
    title: '标题',              // 标题文本
    content: '',                // 内容 HTML
    size: 'md',                 // 尺寸: sm, md, lg, xl
    closable: true,             // 是否可关闭
    backdrop: true,             // 是否显示背景遮罩
    keyboard: true,             // 是否支持 ESC 键关闭
    confirmText: '确认',        // 确认按钮文本
    cancelText: '取消'          // 取消按钮文本
}
```

#### 方法
- `open(options)`: 打开模态框
- `close()`: 关闭模态框
- `confirm()`: 确认操作

#### 事件
- `modal-opened`: 模态框打开事件
- `modal-closed`: 模态框关闭事件
- `modal-confirmed`: 确认操作事件

### Table 组件

#### 功能描述
数据表格组件，支持排序、分页和筛选。

#### 属性配置
```javascript
{
    data: [],                   // 数据数组
    columns: [],                // 列配置数组
    sortKey: '',                // 排序字段
    sortOrder: 'asc',           // 排序方向: asc, desc
    currentPage: 1,             // 当前页码
    pageSize: 10,               // 每页条数
    loading: false,             // 是否加载中
    selectable: false,          // 是否可选择行
    selectedRows: []            // 选中的行
}
```

#### 列配置
```javascript
{
    key: 'name',                // 字段名
    title: '姓名',              // 列标题
    sortable: true,             // 是否可排序
    width: '100px',             // 列宽度
    align: 'left',              // 对齐方式: left, center, right
    render: (item) => {         // 自定义渲染函数
        return `<span>${item.name}</span>`;
    }
}
```

### Form 组件

#### 功能描述
表单组件，支持验证和提交。

#### 属性配置
```javascript
{
    fields: [],                 // 字段配置数组
    data: {},                   // 表单数据
    errors: {},                 // 错误信息
    loading: false,             // 是否提交中
    readonly: false,            // 是否只读
    layout: 'vertical'          // 布局方式: vertical, horizontal, inline
}
```

#### 字段配置
```javascript
{
    name: 'username',           // 字段名
    type: 'text',               // 字段类型
    label: '用户名',            // 标签
    placeholder: '请输入用户名', // 占位符
    required: true,             // 是否必填
    rules: [                    // 验证规则
        { required: true, message: '用户名不能为空' },
        { min: 3, message: '用户名至少3个字符' }
    ]
}
```

## 布局组件

### Navbar 组件

#### 功能描述
导航栏组件，支持响应式布局和多级菜单。

#### 属性配置
```javascript
{
    brand: {                    // 品牌信息
        text: 'Logo',
        href: '/',
        image: ''
    },
    items: [],                  // 菜单项数组
    user: null,                 // 用户信息
    theme: 'light',             // 主题
    fixed: true,                // 是否固定
    transparent: false          // 是否透明
}
```

#### 菜单项配置
```javascript
{
    text: '首页',               // 菜单文本
    href: '/',                  // 链接地址
    icon: 'home',               // 图标
    active: false,              // 是否激活
    children: []                // 子菜单
}
```

### Sidebar 组件

#### 功能描述
侧边栏组件，支持折叠和多级菜单。

#### 属性配置
```javascript
{
    collapsed: false,           // 是否折叠
    items: [],                  // 菜单项
    width: '250px',             // 宽度
    position: 'left',           // 位置: left, right
    overlay: false,             // 是否覆盖模式
    persistent: true            // 是否持久化状态
}
```

## 反馈组件

### Toast 组件

#### 功能描述
消息提示组件，支持多种类型和位置。

#### 属性配置
```javascript
{
    message: '',                // 消息内容
    type: 'info',               // 类型: success, error, warning, info
    duration: 3000,             // 显示时长(ms)
    position: 'top-right',      // 位置
    closable: true,             // 是否可关闭
    icon: true                  // 是否显示图标
}
```

#### 使用方法
```javascript
// 显示成功消息
Alpine.store('toast').show({
    message: '操作成功！',
    type: 'success'
});

// 显示错误消息
Alpine.store('toast').error('操作失败！');
```

### Loading 组件

#### 功能描述
加载指示器组件，支持多种样式。

#### 属性配置
```javascript
{
    loading: false,             // 是否显示
    text: '加载中...',          // 加载文本
    size: 'md',                 // 尺寸: sm, md, lg
    overlay: false,             // 是否全屏遮罩
    spinner: 'default'          // 加载动画类型
}
```

## 工具函数

### 验证工具
```javascript
const validators = {
    required: (value) => !!value || '此字段为必填项',
    email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value) || '请输入有效邮箱',
    minLength: (min) => (value) => value.length >= min || `最少${min}个字符`,
    maxLength: (max) => (value) => value.length <= max || `最多${max}个字符`,
    pattern: (regex, message) => (value) => regex.test(value) || message
};
```

### 格式化工具
```javascript
const formatters = {
    date: (date, format = 'YYYY-MM-DD') => {
        // 日期格式化
    },
    currency: (amount, currency = 'CNY') => {
        // 货币格式化
    },
    number: (num, decimals = 2) => {
        // 数字格式化
    }
};
```

### 存储工具
```javascript
const storage = {
    set: (key, value) => localStorage.setItem(key, JSON.stringify(value)),
    get: (key) => JSON.parse(localStorage.getItem(key) || 'null'),
    remove: (key) => localStorage.removeItem(key),
    clear: () => localStorage.clear()
};
```

## 最佳实践

### 1. 组件开发
- 保持组件的单一职责
- 使用 Alpine.js 的响应式特性
- 合理使用 DaisyUI 的样式类
- 添加适当的错误处理

### 2. 性能优化
- 避免不必要的重新渲染
- 使用 `x-show` 而不是 `x-if` 来切换显示
- 合理使用 `x-transition` 添加动画
- 延迟加载非关键组件

### 3. 可访问性
- 添加适当的 ARIA 属性
- 支持键盘导航
- 提供屏幕阅读器支持
- 确保颜色对比度符合标准

### 4. 测试策略
- 编写组件单元测试
- 测试用户交互流程
- 验证响应式布局
- 检查浏览器兼容性
