# DaisyUI + Alpine.js 组件文档

## 组件开发指南

### 组件设计原则

1. **单一职责**: 每个组件只负责一个特定功能
2. **可复用性**: 组件应该能在不同场景下复用
3. **可配置性**: 通过属性配置组件行为和外观
4. **事件驱动**: 使用事件进行组件间通信
5. **响应式**: 支持不同屏幕尺寸的适配

### 组件命名规范

- 组件函数使用驼峰命名: `buttonComponent`, `modalComponent`
- 组件文件使用短横线命名: `button-component.js`, `modal-component.js`
- CSS 类名遵循 DaisyUI 规范: `btn`, `modal`, `form-control`

## 基础组件

### Button 组件

#### 功能描述
可配置的按钮组件，支持多种样式、尺寸和状态。

#### 属性配置
```javascript
{
    text: 'Click me',           // 按钮文本
    variant: 'primary',         // 样式变体: primary, secondary, accent, ghost, link
    size: 'md',                 // 尺寸: xs, sm, md, lg
    disabled: false,            // 是否禁用
    loading: false,             // 是否显示加载状态
    icon: '',                   // 图标类名
    iconPosition: 'left'        // 图标位置: left, right
}
```

#### 事件
- `button-click`: 按钮点击事件，传递 `{ variant, text }` 数据

#### 使用示例
```html
<div x-data="buttonComponent({ 
    text: '提交', 
    variant: 'primary', 
    size: 'lg' 
})" @button-click="handleSubmit">
</div>
```

### Input 组件

#### 功能描述
表单输入组件，支持多种输入类型和验证。

#### 属性配置
```javascript
{
    type: 'text',               // 输入类型: text, email, password, number
    label: '',                  // 标签文本
    placeholder: '',            // 占位符文本
    value: '',                  // 输入值
    error: '',                  // 错误信息
    required: false,            // 是否必填
    disabled: false,            // 是否禁用
    maxLength: null,            // 最大长度
    pattern: null,              // 验证正则表达式
    helpText: ''                // 帮助文本
}
```

#### 验证规则
- 必填验证: `required: true`
- 邮箱验证: `type: 'email'`
- 自定义验证: `pattern: /regex/`
- 长度验证: `maxLength: number`

#### 事件
- `input-change`: 输入值变化事件
- `input-blur`: 失去焦点事件
- `validation-error`: 验证失败事件

### Select 组件

#### 功能描述
下拉选择组件，支持单选和多选。

#### 属性配置
```javascript
{
    options: [],                // 选项数组 [{ value, label }]
    value: '',                  // 选中值
    multiple: false,            // 是否多选
    placeholder: '请选择',       // 占位符
    searchable: false,          // 是否可搜索
    disabled: false,            // 是否禁用
    clearable: true             // 是否可清空
}
```

#### 使用示例
```html
<div x-data="selectComponent({
    options: [
        { value: '1', label: '选项1' },
        { value: '2', label: '选项2' }
    ],
    placeholder: '请选择选项'
})">
</div>
```

## 复合组件

### Modal 组件

#### 功能描述
模态对话框组件，支持自定义内容和操作。

#### 属性配置
```javascript
{
    isOpen: false,              // 是否打开
    title: '标题',              // 标题文本
    content: '',                // 内容 HTML
    size: 'md',                 // 尺寸: sm, md, lg, xl
    closable: true,             // 是否可关闭
    backdrop: true,             // 是否显示背景遮罩
    keyboard: true,             // 是否支持 ESC 键关闭
    confirmText: '确认',        // 确认按钮文本
    cancelText: '取消'          // 取消按钮文本
}
```

#### 方法
- `open(options)`: 打开模态框
- `close()`: 关闭模态框
- `confirm()`: 确认操作

#### 事件
- `modal-opened`: 模态框打开事件
- `modal-closed`: 模态框关闭事件
- `modal-confirmed`: 确认操作事件

### Table 组件

#### 功能描述
数据表格组件，支持排序、分页和筛选。

#### 属性配置
```javascript
{
    data: [],                   // 数据数组
    columns: [],                // 列配置数组
    sortKey: '',                // 排序字段
    sortOrder: 'asc',           // 排序方向: asc, desc
    currentPage: 1,             // 当前页码
    pageSize: 10,               // 每页条数
    loading: false,             // 是否加载中
    selectable: false,          // 是否可选择行
    selectedRows: []            // 选中的行
}
```

#### 列配置
```javascript
{
    key: 'name',                // 字段名
    title: '姓名',              // 列标题
    sortable: true,             // 是否可排序
    width: '100px',             // 列宽度
    align: 'left',              // 对齐方式: left, center, right
    render: (item) => {         // 自定义渲染函数
        return `<span>${item.name}</span>`;
    }
}
```

### Form 组件

#### 功能描述
表单组件，支持验证和提交。

#### 属性配置
```javascript
{
    fields: [],                 // 字段配置数组
    data: {},                   // 表单数据
    errors: {},                 // 错误信息
    loading: false,             // 是否提交中
    readonly: false,            // 是否只读
    layout: 'vertical'          // 布局方式: vertical, horizontal, inline
}
```

#### 字段配置
```javascript
{
    name: 'username',           // 字段名
    type: 'text',               // 字段类型
    label: '用户名',            // 标签
    placeholder: '请输入用户名', // 占位符
    required: true,             // 是否必填
    rules: [                    // 验证规则
        { required: true, message: '用户名不能为空' },
        { min: 3, message: '用户名至少3个字符' }
    ]
}
```

## 布局组件

### Navbar 组件

#### 功能描述
导航栏组件，支持响应式布局和多级菜单。

#### 属性配置
```javascript
{
    brand: {                    // 品牌信息
        text: 'Logo',
        href: '/',
        image: ''
    },
    items: [],                  // 菜单项数组
    user: null,                 // 用户信息
    theme: 'light',             // 主题
    fixed: true,                // 是否固定
    transparent: false          // 是否透明
}
```

#### 菜单项配置
```javascript
{
    text: '首页',               // 菜单文本
    href: '/',                  // 链接地址
    icon: 'home',               // 图标
    active: false,              // 是否激活
    children: []                // 子菜单
}
```

### Sidebar 组件

#### 功能描述
侧边栏组件，支持折叠和多级菜单。

#### 属性配置
```javascript
{
    collapsed: false,           // 是否折叠
    items: [],                  // 菜单项
    width: '250px',             // 宽度
    position: 'left',           // 位置: left, right
    overlay: false,             // 是否覆盖模式
    persistent: true            // 是否持久化状态
}
```

## 反馈组件

### Toast 组件

#### 功能描述
消息提示组件，支持多种类型和位置。

#### 属性配置
```javascript
{
    message: '',                // 消息内容
    type: 'info',               // 类型: success, error, warning, info
    duration: 3000,             // 显示时长(ms)
    position: 'top-right',      // 位置
    closable: true,             // 是否可关闭
    icon: true                  // 是否显示图标
}
```

#### 使用方法
```javascript
// 显示成功消息
Alpine.store('toast').show({
    message: '操作成功！',
    type: 'success'
});

// 显示错误消息
Alpine.store('toast').error('操作失败！');
```

### Loading 组件

#### 功能描述
加载指示器组件，支持多种样式。

#### 属性配置
```javascript
{
    loading: false,             // 是否显示
    text: '加载中...',          // 加载文本
    size: 'md',                 // 尺寸: sm, md, lg
    overlay: false,             // 是否全屏遮罩
    spinner: 'default'          // 加载动画类型
}
```

## 工具函数

### 验证工具
```javascript
const validators = {
    required: (value) => !!value || '此字段为必填项',
    email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value) || '请输入有效邮箱',
    minLength: (min) => (value) => value.length >= min || `最少${min}个字符`,
    maxLength: (max) => (value) => value.length <= max || `最多${max}个字符`,
    pattern: (regex, message) => (value) => regex.test(value) || message
};
```

### 格式化工具
```javascript
const formatters = {
    date: (date, format = 'YYYY-MM-DD') => {
        // 日期格式化
    },
    currency: (amount, currency = 'CNY') => {
        // 货币格式化
    },
    number: (num, decimals = 2) => {
        // 数字格式化
    }
};
```

### 存储工具
```javascript
const storage = {
    set: (key, value) => localStorage.setItem(key, JSON.stringify(value)),
    get: (key) => JSON.parse(localStorage.getItem(key) || 'null'),
    remove: (key) => localStorage.removeItem(key),
    clear: () => localStorage.clear()
};
```

## 最佳实践

### 1. 组件开发
- 保持组件的单一职责
- 使用 Alpine.js 的响应式特性
- 合理使用 DaisyUI 的样式类
- 添加适当的错误处理

### 2. 性能优化
- 避免不必要的重新渲染
- 使用 `x-show` 而不是 `x-if` 来切换显示
- 合理使用 `x-transition` 添加动画
- 延迟加载非关键组件

### 3. 可访问性
- 添加适当的 ARIA 属性
- 支持键盘导航
- 提供屏幕阅读器支持
- 确保颜色对比度符合标准

### 4. 测试策略
- 编写组件单元测试
- 测试用户交互流程
- 验证响应式布局
- 检查浏览器兼容性
