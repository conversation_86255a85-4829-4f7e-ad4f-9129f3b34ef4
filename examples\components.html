<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="DaisyUI 组件展示页面">
    <title>组件展示 - DaisyUI + Alpine.js</title>
    
    <!-- DaisyUI + Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.4.24/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js"></script>
    
    <!-- 应用数据存储 -->
    <script src="../data/stores.js"></script>
</head>
<body class="min-h-screen bg-base-100">
    <!-- 主应用容器 -->
    <div id="app" x-data="componentsDemo" x-init="init()">
        
        <!-- 顶部导航 -->
        <header class="navbar bg-base-100 shadow-sm border-b border-base-200">
            <nav class="navbar-start">
                <a href="../index.html" class="btn btn-ghost">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    返回首页
                </a>
            </nav>
            
            <section class="navbar-center">
                <h1 class="text-xl font-bold">DaisyUI 组件展示</h1>
            </section>
            
            <aside class="navbar-end">
                <!-- 主题切换 -->
                <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-ghost btn-circle">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                        </svg>
                    </div>
                    <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                        <template x-for="theme in popularThemes" :key="theme">
                            <li>
                                <a @click="$store.theme.set(theme)" 
                                   :class="{ 'active': theme === $store.theme.current }"
                                   x-text="theme">
                                </a>
                            </li>
                        </template>
                    </ul>
                </div>
            </aside>
        </header>
        
        <!-- 主内容区域 -->
        <main class="container mx-auto p-4">
            
            <!-- 组件分类导航 -->
            <nav class="tabs tabs-boxed mb-8 justify-center">
                <template x-for="category in categories" :key="category.id">
                    <a class="tab" 
                       :class="{ 'tab-active': activeCategory === category.id }"
                       @click="setActiveCategory(category.id)"
                       x-text="category.name">
                    </a>
                </template>
            </nav>
            
            <!-- 按钮组件展示 -->
            <section x-show="activeCategory === 'buttons'" class="space-y-8">
                <header>
                    <h2 class="text-2xl font-bold mb-4">按钮组件</h2>
                    <p class="text-base-content/60 mb-6">DaisyUI 提供了丰富的按钮样式和状态</p>
                </header>
                
                <!-- 基础按钮 -->
                <article class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <h3 class="card-title">基础按钮</h3>
                        <div class="flex flex-wrap gap-2">
                            <button class="btn">默认</button>
                            <button class="btn btn-primary">主要</button>
                            <button class="btn btn-secondary">次要</button>
                            <button class="btn btn-accent">强调</button>
                            <button class="btn btn-ghost">幽灵</button>
                            <button class="btn btn-link">链接</button>
                        </div>
                    </div>
                </article>
                
                <!-- 按钮尺寸 -->
                <article class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <h3 class="card-title">按钮尺寸</h3>
                        <div class="flex flex-wrap items-center gap-2">
                            <button class="btn btn-xs">超小</button>
                            <button class="btn btn-sm">小</button>
                            <button class="btn">默认</button>
                            <button class="btn btn-lg">大</button>
                        </div>
                    </div>
                </article>
                
                <!-- 按钮状态 -->
                <article class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <h3 class="card-title">按钮状态</h3>
                        <div class="flex flex-wrap gap-2">
                            <button class="btn btn-primary">正常</button>
                            <button class="btn btn-primary loading">加载中</button>
                            <button class="btn btn-primary" disabled>禁用</button>
                            <button class="btn btn-outline btn-primary">轮廓</button>
                        </div>
                    </div>
                </article>
            </section>
            
            <!-- 表单组件展示 -->
            <section x-show="activeCategory === 'forms'" class="space-y-8">
                <header>
                    <h2 class="text-2xl font-bold mb-4">表单组件</h2>
                    <p class="text-base-content/60 mb-6">语义化的表单控件和布局</p>
                </header>
                
                <!-- 输入框 -->
                <article class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <h3 class="card-title">输入框</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <fieldset class="form-control w-full">
                                <label class="label" for="demo-input-1">
                                    <span class="label-text">基础输入框</span>
                                </label>
                                <input id="demo-input-1" type="text" placeholder="请输入内容" class="input input-bordered w-full" />
                            </fieldset>
                            
                            <fieldset class="form-control w-full">
                                <label class="label" for="demo-input-2">
                                    <span class="label-text">主要样式</span>
                                </label>
                                <input id="demo-input-2" type="text" placeholder="请输入内容" class="input input-bordered input-primary w-full" />
                            </fieldset>
                            
                            <fieldset class="form-control w-full">
                                <label class="label" for="demo-input-3">
                                    <span class="label-text">成功状态</span>
                                </label>
                                <input id="demo-input-3" type="text" placeholder="请输入内容" class="input input-bordered input-success w-full" />
                            </fieldset>
                            
                            <fieldset class="form-control w-full">
                                <label class="label" for="demo-input-4">
                                    <span class="label-text">错误状态</span>
                                </label>
                                <input id="demo-input-4" type="text" placeholder="请输入内容" class="input input-bordered input-error w-full" />
                                <label class="label">
                                    <span class="label-text-alt text-error">错误提示信息</span>
                                </label>
                            </fieldset>
                        </div>
                    </div>
                </article>
                
                <!-- 选择框和复选框 -->
                <article class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <h3 class="card-title">选择控件</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <fieldset class="form-control w-full">
                                <label class="label" for="demo-select">
                                    <span class="label-text">下拉选择</span>
                                </label>
                                <select id="demo-select" class="select select-bordered w-full">
                                    <option disabled selected>请选择选项</option>
                                    <option>选项 1</option>
                                    <option>选项 2</option>
                                    <option>选项 3</option>
                                </select>
                            </fieldset>
                            
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">复选框和单选框</span>
                                </label>
                                <div class="space-y-2">
                                    <label class="label cursor-pointer justify-start gap-2">
                                        <input type="checkbox" class="checkbox checkbox-primary" />
                                        <span class="label-text">复选框选项</span>
                                    </label>
                                    <label class="label cursor-pointer justify-start gap-2">
                                        <input type="radio" name="demo-radio" class="radio radio-primary" />
                                        <span class="label-text">单选框选项 1</span>
                                    </label>
                                    <label class="label cursor-pointer justify-start gap-2">
                                        <input type="radio" name="demo-radio" class="radio radio-primary" />
                                        <span class="label-text">单选框选项 2</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </article>
            </section>
            
            <!-- 数据展示组件 -->
            <section x-show="activeCategory === 'data'" class="space-y-8">
                <header>
                    <h2 class="text-2xl font-bold mb-4">数据展示</h2>
                    <p class="text-base-content/60 mb-6">表格、卡片和统计数据展示</p>
                </header>
                
                <!-- 表格 -->
                <article class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <h3 class="card-title">数据表格</h3>
                        <div class="overflow-x-auto">
                            <table class="table table-zebra">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>姓名</th>
                                        <th>邮箱</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <template x-for="user in sampleUsers" :key="user.id">
                                        <tr>
                                            <td x-text="user.id"></td>
                                            <td x-text="user.name"></td>
                                            <td x-text="user.email"></td>
                                            <td>
                                                <span class="badge" 
                                                      :class="{
                                                          'badge-success': user.status === 'active',
                                                          'badge-warning': user.status === 'pending',
                                                          'badge-error': user.status === 'inactive'
                                                      }"
                                                      x-text="user.status">
                                                </span>
                                            </td>
                                            <td>
                                                <button class="btn btn-xs btn-primary">编辑</button>
                                                <button class="btn btn-xs btn-error">删除</button>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </article>
                
                <!-- 统计卡片 -->
                <article class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <h3 class="card-title">统计数据</h3>
                        <div class="stats shadow">
                            <div class="stat">
                                <div class="stat-figure text-primary">
                                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="stat-title">总用户数</div>
                                <div class="stat-value text-primary">25.6K</div>
                                <div class="stat-desc">21% 比上月增长</div>
                            </div>
                            
                            <div class="stat">
                                <div class="stat-figure text-secondary">
                                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                                    </svg>
                                </div>
                                <div class="stat-title">新用户</div>
                                <div class="stat-value text-secondary">2.6K</div>
                                <div class="stat-desc">21% 比上月增长</div>
                            </div>
                            
                            <div class="stat">
                                <div class="stat-figure text-accent">
                                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                                    </svg>
                                </div>
                                <div class="stat-title">订单数</div>
                                <div class="stat-value">86%</div>
                                <div class="stat-desc text-accent">31 个任务剩余</div>
                            </div>
                        </div>
                    </div>
                </article>
            </section>
            
            <!-- 反馈组件 -->
            <section x-show="activeCategory === 'feedback'" class="space-y-8">
                <header>
                    <h2 class="text-2xl font-bold mb-4">反馈组件</h2>
                    <p class="text-base-content/60 mb-6">模态框、警告和通知组件</p>
                </header>
                
                <!-- 警告框 -->
                <article class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <h3 class="card-title">警告提示</h3>
                        <div class="space-y-4">
                            <div class="alert alert-info">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                                <span>这是一条信息提示</span>
                            </div>
                            
                            <div class="alert alert-success">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                <span>操作成功完成！</span>
                            </div>
                            
                            <div class="alert alert-warning">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                <span>请注意这个警告信息</span>
                            </div>
                            
                            <div class="alert alert-error">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                </svg>
                                <span>发生了一个错误</span>
                            </div>
                        </div>
                    </div>
                </article>
                
                <!-- 模态框演示 -->
                <article class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <h3 class="card-title">模态框</h3>
                        <div class="space-x-2">
                            <button class="btn btn-primary" onclick="demo_modal.showModal()">打开模态框</button>
                            <button class="btn btn-secondary" @click="showNotification('info', '这是一条通知消息')">显示通知</button>
                        </div>
                    </div>
                </article>
            </section>
        </main>
        
        <!-- 演示模态框 -->
        <dialog id="demo_modal" class="modal">
            <div class="modal-box">
                <h3 class="font-bold text-lg">演示模态框</h3>
                <p class="py-4">这是一个使用 DaisyUI 原生模态框组件的示例。</p>
                <div class="modal-action">
                    <form method="dialog">
                        <button class="btn">关闭</button>
                        <button class="btn btn-primary">确认</button>
                    </form>
                </div>
            </div>
        </dialog>
    </div>
    
    <!-- 通知系统 -->
    <div class="toast toast-top toast-end z-50">
        <template x-for="notification in $store.notifications.items" :key="notification.id">
            <div class="alert" 
                 :class="{
                     'alert-success': notification.type === 'success',
                     'alert-error': notification.type === 'error',
                     'alert-warning': notification.type === 'warning',
                     'alert-info': notification.type === 'info'
                 }">
                <span x-text="notification.message"></span>
                <button class="btn btn-sm btn-circle btn-ghost" 
                        @click="$store.notifications.remove(notification.id)">✕</button>
            </div>
        </template>
    </div>
    
    <!-- 页面脚本 -->
    <script>
        function componentsDemo() {
            return {
                activeCategory: 'buttons',
                
                categories: [
                    { id: 'buttons', name: '按钮' },
                    { id: 'forms', name: '表单' },
                    { id: 'data', name: '数据展示' },
                    { id: 'feedback', name: '反馈' }
                ],
                
                popularThemes: ['light', 'dark', 'cupcake', 'corporate', 'synthwave', 'retro'],
                
                sampleUsers: [
                    { id: 1, name: '张三', email: '<EMAIL>', status: 'active' },
                    { id: 2, name: '李四', email: '<EMAIL>', status: 'pending' },
                    { id: 3, name: '王五', email: '<EMAIL>', status: 'inactive' },
                    { id: 4, name: '赵六', email: '<EMAIL>', status: 'active' }
                ],
                
                init() {
                    this.$store.theme.init();
                    this.$store.notifications.success('欢迎查看 DaisyUI 组件展示！');
                },
                
                setActiveCategory(categoryId) {
                    this.activeCategory = categoryId;
                },
                
                showNotification(type, message) {
                    this.$store.notifications.add({ type, message });
                }
            };
        }
    </script>
</body>
</html>
