# DaisyUI + Alpine.js 部署指南

## 部署概述

本项目是一个基于 CDN 的纯前端应用，支持多种部署方式。由于使用了 CDN 引入依赖，部署过程相对简单，无需复杂的构建流程。

## 部署环境要求

### 基础要求
- **Web 服务器**: 支持静态文件服务
- **HTTPS**: 生产环境建议使用 HTTPS
- **现代浏览器**: Chrome 90+, Firefox 88+, Safari 14+

### 可选要求
- **CDN**: 用于加速静态资源
- **负载均衡**: 高并发场景
- **监控系统**: 性能和错误监控

## 静态文件部署

### 1. 传统 Web 服务器

#### Apache 部署
```apache
# .htaccess 文件配置
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # 处理 SPA 路由
    RewriteBase /
    RewriteRule ^index\.html$ - [L]
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule . /index.html [L]
</IfModule>

# 缓存配置
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>

# Gzip 压缩
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
```

#### Nginx 部署
```nginx
# nginx.conf 配置
server {
    listen 80;
    server_name example.com;
    root /var/www/html;
    index index.html;
    
    # SPA 路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 静态资源缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
}
```

### 2. 云平台部署

#### Vercel 部署
```json
// vercel.json
{
    "version": 2,
    "builds": [
        {
            "src": "**/*",
            "use": "@vercel/static"
        }
    ],
    "routes": [
        {
            "src": "/(.*)",
            "dest": "/index.html"
        }
    ],
    "headers": [
        {
            "source": "/(.*)",
            "headers": [
                {
                    "key": "X-Content-Type-Options",
                    "value": "nosniff"
                },
                {
                    "key": "X-Frame-Options",
                    "value": "DENY"
                },
                {
                    "key": "X-XSS-Protection",
                    "value": "1; mode=block"
                }
            ]
        },
        {
            "source": "/assets/(.*)",
            "headers": [
                {
                    "key": "Cache-Control",
                    "value": "public, max-age=31536000, immutable"
                }
            ]
        }
    ]
}
```

#### Netlify 部署
```toml
# netlify.toml
[build]
  publish = "."
  command = "echo 'No build required'"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
```

#### GitHub Pages 部署
```yaml
# .github/workflows/deploy.yml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build
      run: npm run build
    
    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      if: github.ref == 'refs/heads/main'
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./dist
```

## 容器化部署

### Docker 部署
```dockerfile
# Dockerfile
FROM nginx:alpine

# 复制静态文件
COPY . /usr/share/nginx/html

# 复制 Nginx 配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 暴露端口
EXPOSE 80

# 启动 Nginx
CMD ["nginx", "-g", "daemon off;"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    restart: unless-stopped
    
  # 可选：添加 SSL 终止
  ssl:
    image: nginx:alpine
    ports:
      - "443:443"
    volumes:
      - ./ssl.conf:/etc/nginx/conf.d/default.conf
      - ./certs:/etc/nginx/certs
    depends_on:
      - web
    restart: unless-stopped
```

### Kubernetes 部署
```yaml
# k8s-deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: daisyui-alpine-app
  labels:
    app: daisyui-alpine-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: daisyui-alpine-app
  template:
    metadata:
      labels:
        app: daisyui-alpine-app
    spec:
      containers:
      - name: web
        image: your-registry/daisyui-alpine-app:latest
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "64Mi"
            cpu: "250m"
          limits:
            memory: "128Mi"
            cpu: "500m"

---
apiVersion: v1
kind: Service
metadata:
  name: daisyui-alpine-service
spec:
  selector:
    app: daisyui-alpine-app
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: LoadBalancer

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: daisyui-alpine-ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - example.com
    secretName: daisyui-alpine-tls
  rules:
  - host: example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: daisyui-alpine-service
            port:
              number: 80
```

## 环境配置

### 1. 环境变量配置
```javascript
// config/environment.js
const environments = {
    development: {
        API_BASE_URL: 'http://localhost:3001/api',
        APP_URL: 'http://localhost:3000',
        DEBUG: true,
        CDN_URL: '',
        ANALYTICS_ID: ''
    },
    
    staging: {
        API_BASE_URL: 'https://staging-api.example.com/api',
        APP_URL: 'https://staging.example.com',
        DEBUG: false,
        CDN_URL: 'https://cdn.example.com',
        ANALYTICS_ID: 'GA-STAGING-ID'
    },
    
    production: {
        API_BASE_URL: 'https://api.example.com/api',
        APP_URL: 'https://example.com',
        DEBUG: false,
        CDN_URL: 'https://cdn.example.com',
        ANALYTICS_ID: 'GA-PRODUCTION-ID'
    }
};

// 获取当前环境配置
function getConfig() {
    const env = process.env.NODE_ENV || 'development';
    return environments[env] || environments.development;
}

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { getConfig, environments };
} else {
    window.APP_CONFIG = getConfig();
}
```

### 2. 构建脚本
```javascript
// scripts/build.js
const fs = require('fs');
const path = require('path');

class Builder {
    constructor(env = 'production') {
        this.env = env;
        this.srcDir = path.join(__dirname, '..');
        this.distDir = path.join(__dirname, '../dist');
    }
    
    async build() {
        console.log(`Building for ${this.env} environment...`);
        
        // 清理输出目录
        await this.clean();
        
        // 创建输出目录
        await this.createDist();
        
        // 复制文件
        await this.copyFiles();
        
        // 处理 HTML
        await this.processHTML();
        
        // 压缩资源
        await this.minifyAssets();
        
        console.log('Build completed!');
    }
    
    async clean() {
        if (fs.existsSync(this.distDir)) {
            fs.rmSync(this.distDir, { recursive: true });
        }
    }
    
    async createDist() {
        fs.mkdirSync(this.distDir, { recursive: true });
    }
    
    async copyFiles() {
        const filesToCopy = [
            'index.html',
            'assets/',
            'components/',
            'pages/'
        ];
        
        for (const file of filesToCopy) {
            const src = path.join(this.srcDir, file);
            const dest = path.join(this.distDir, file);
            
            if (fs.existsSync(src)) {
                if (fs.statSync(src).isDirectory()) {
                    this.copyDir(src, dest);
                } else {
                    fs.copyFileSync(src, dest);
                }
            }
        }
    }
    
    copyDir(src, dest) {
        fs.mkdirSync(dest, { recursive: true });
        
        const files = fs.readdirSync(src);
        for (const file of files) {
            const srcFile = path.join(src, file);
            const destFile = path.join(dest, file);
            
            if (fs.statSync(srcFile).isDirectory()) {
                this.copyDir(srcFile, destFile);
            } else {
                fs.copyFileSync(srcFile, destFile);
            }
        }
    }
    
    async processHTML() {
        const htmlFile = path.join(this.distDir, 'index.html');
        let html = fs.readFileSync(htmlFile, 'utf8');
        
        // 替换环境变量
        const config = require('../config/environment').environments[this.env];
        
        Object.keys(config).forEach(key => {
            const placeholder = `{{${key}}}`;
            html = html.replace(new RegExp(placeholder, 'g'), config[key]);
        });
        
        // 添加环境标识
        html = html.replace(
            '<head>',
            `<head>\n    <meta name="environment" content="${this.env}">`
        );
        
        fs.writeFileSync(htmlFile, html);
    }
    
    async minifyAssets() {
        // 压缩 CSS
        const cssDir = path.join(this.distDir, 'assets/css');
        if (fs.existsSync(cssDir)) {
            const cssFiles = fs.readdirSync(cssDir).filter(f => f.endsWith('.css'));
            
            for (const file of cssFiles) {
                const filePath = path.join(cssDir, file);
                let css = fs.readFileSync(filePath, 'utf8');
                
                // 简单的 CSS 压缩
                css = css
                    .replace(/\/\*[\s\S]*?\*\//g, '')
                    .replace(/\s+/g, ' ')
                    .replace(/;\s*}/g, '}')
                    .trim();
                
                fs.writeFileSync(filePath, css);
            }
        }
        
        // 压缩 JS
        const jsDir = path.join(this.distDir, 'assets/js');
        if (fs.existsSync(jsDir)) {
            this.minifyJSDir(jsDir);
        }
    }
    
    minifyJSDir(dir) {
        const files = fs.readdirSync(dir);
        
        for (const file of files) {
            const filePath = path.join(dir, file);
            
            if (fs.statSync(filePath).isDirectory()) {
                this.minifyJSDir(filePath);
            } else if (file.endsWith('.js')) {
                let js = fs.readFileSync(filePath, 'utf8');
                
                // 简单的 JS 压缩
                js = js
                    .replace(/\/\*[\s\S]*?\*\//g, '')
                    .replace(/\/\/.*$/gm, '')
                    .replace(/\s+/g, ' ')
                    .trim();
                
                fs.writeFileSync(filePath, js);
            }
        }
    }
}

// 执行构建
const env = process.argv[2] || 'production';
const builder = new Builder(env);
builder.build().catch(console.error);
```

## 性能优化

### 1. 缓存策略
```javascript
// 缓存配置
const cacheConfig = {
    // 静态资源长期缓存
    assets: {
        'Cache-Control': 'public, max-age=31536000, immutable',
        'ETag': true
    },
    
    // HTML 文件短期缓存
    html: {
        'Cache-Control': 'public, max-age=3600',
        'ETag': true
    },
    
    // API 响应缓存
    api: {
        'Cache-Control': 'public, max-age=300',
        'Vary': 'Accept-Encoding'
    }
};
```

### 2. CDN 配置
```javascript
// CDN 资源配置
const cdnConfig = {
    development: {
        enabled: false,
        baseURL: ''
    },
    
    production: {
        enabled: true,
        baseURL: 'https://cdn.example.com',
        resources: {
            css: '/assets/css/',
            js: '/assets/js/',
            images: '/assets/images/'
        }
    }
};
```

## 监控和日志

### 1. 错误监控
```javascript
// 错误监控配置
window.addEventListener('error', (event) => {
    const errorData = {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: new Date().toISOString()
    };
    
    // 发送错误报告
    if (window.APP_CONFIG?.ERROR_REPORTING_URL) {
        fetch(window.APP_CONFIG.ERROR_REPORTING_URL, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(errorData)
        }).catch(console.error);
    }
});
```

### 2. 性能监控
```javascript
// 性能监控
window.addEventListener('load', () => {
    setTimeout(() => {
        const perfData = performance.getEntriesByType('navigation')[0];
        const metrics = {
            loadTime: perfData.loadEventEnd - perfData.loadEventStart,
            domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
            firstPaint: performance.getEntriesByType('paint')[0]?.startTime,
            firstContentfulPaint: performance.getEntriesByType('paint')[1]?.startTime
        };
        
        // 发送性能数据
        if (window.APP_CONFIG?.ANALYTICS_URL) {
            fetch(window.APP_CONFIG.ANALYTICS_URL, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(metrics)
            }).catch(console.error);
        }
    }, 0);
});
```

## 安全配置

### 1. 安全头配置
```nginx
# 安全头配置
add_header X-Frame-Options "DENY" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' cdn.jsdelivr.net cdn.tailwindcss.com; style-src 'self' 'unsafe-inline' cdn.jsdelivr.net; img-src 'self' data: https:; font-src 'self' data:;" always;
```

### 2. HTTPS 配置
```nginx
# SSL 配置
server {
    listen 443 ssl http2;
    server_name example.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=63072000" always;
    
    # 重定向 HTTP 到 HTTPS
    if ($scheme != "https") {
        return 301 https://$host$request_uri;
    }
}
```

这个部署指南涵盖了从简单的静态文件部署到复杂的容器化部署的各种场景，包括性能优化、监控和安全配置等重要方面。
