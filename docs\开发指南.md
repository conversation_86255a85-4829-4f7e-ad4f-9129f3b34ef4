# DaisyUI + Alpine.js 开发指南

## 快速开始

### 环境要求
- 现代浏览器 (Chrome 90+, Firefox 88+, Safari 14+)
- 本地开发服务器 (可选)
- 代码编辑器 (推荐 VS Code)

### 项目初始化
```bash
# 克隆项目
git clone <repository-url>
cd TYKJ

# 安装依赖 (如果使用包管理器)
npm install

# 启动开发服务器
npm run dev
# 或者直接用浏览器打开 index.html
```

### 基础配置
```html
<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DaisyUI + Alpine.js 应用</title>
    
    <!-- DaisyUI + Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.4.24/dist/full.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js"></script>
    
    <!-- 自定义样式 -->
    <link href="./assets/css/main.css" rel="stylesheet">
</head>
<body>
    <!-- 应用内容 -->
    <div id="app" x-data="app()">
        <!-- 页面内容 -->
    </div>
    
    <!-- 应用脚本 -->
    <script src="./assets/js/app.js"></script>
</body>
</html>
```

## 组件开发

### 1. 基础组件开发

#### 组件结构
```javascript
// assets/js/components/base/button.js
function buttonComponent(config = {}) {
    return {
        // 默认配置
        text: config.text || 'Button',
        variant: config.variant || 'primary',
        size: config.size || 'md',
        disabled: config.disabled || false,
        loading: config.loading || false,
        icon: config.icon || '',
        
        // 计算属性
        get classes() {
            return [
                'btn',
                `btn-${this.variant}`,
                `btn-${this.size}`,
                this.loading ? 'loading' : '',
                this.disabled ? 'btn-disabled' : ''
            ].filter(Boolean).join(' ');
        },
        
        // 初始化方法
        init() {
            console.log('Button component initialized');
        },
        
        // 事件处理
        handleClick() {
            if (this.disabled || this.loading) return;
            
            this.$dispatch('button-click', {
                variant: this.variant,
                text: this.text
            });
        },
        
        // 公共方法
        setLoading(loading) {
            this.loading = loading;
        },
        
        setDisabled(disabled) {
            this.disabled = disabled;
        }
    };
}

// 注册组件
if (typeof window !== 'undefined') {
    window.buttonComponent = buttonComponent;
}
```

#### 组件使用
```html
<!-- 基础用法 -->
<div x-data="buttonComponent({ text: '点击我', variant: 'primary' })">
    <button :class="classes" 
            :disabled="disabled" 
            @click="handleClick"
            x-text="text">
    </button>
</div>

<!-- 带图标的按钮 -->
<div x-data="buttonComponent({ text: '保存', icon: 'save', variant: 'success' })">
    <button :class="classes" @click="handleClick">
        <i x-show="icon" :class="icon"></i>
        <span x-text="text"></span>
    </button>
</div>
```

### 2. 复合组件开发

#### 表单组件示例
```javascript
// assets/js/components/form/form.js
function formComponent(config = {}) {
    return {
        // 表单配置
        fields: config.fields || [],
        data: config.data || {},
        errors: {},
        loading: false,
        
        // 初始化
        init() {
            this.initializeData();
            this.setupValidation();
        },
        
        // 初始化数据
        initializeData() {
            this.fields.forEach(field => {
                if (!(field.name in this.data)) {
                    this.data[field.name] = field.defaultValue || '';
                }
            });
        },
        
        // 设置验证
        setupValidation() {
            this.fields.forEach(field => {
                if (field.rules) {
                    this.$watch(`data.${field.name}`, () => {
                        this.validateField(field.name);
                    });
                }
            });
        },
        
        // 字段验证
        validateField(fieldName) {
            const field = this.fields.find(f => f.name === fieldName);
            if (!field || !field.rules) return true;
            
            const value = this.data[fieldName];
            this.errors[fieldName] = '';
            
            for (const rule of field.rules) {
                if (rule.required && !value) {
                    this.errors[fieldName] = rule.message || '此字段为必填项';
                    return false;
                }
                
                if (rule.min && value.length < rule.min) {
                    this.errors[fieldName] = rule.message || `最少${rule.min}个字符`;
                    return false;
                }
                
                if (rule.pattern && !rule.pattern.test(value)) {
                    this.errors[fieldName] = rule.message || '格式不正确';
                    return false;
                }
            }
            
            return true;
        },
        
        // 表单验证
        validate() {
            let isValid = true;
            this.fields.forEach(field => {
                if (!this.validateField(field.name)) {
                    isValid = false;
                }
            });
            return isValid;
        },
        
        // 提交表单
        async submit() {
            if (!this.validate()) {
                this.$dispatch('form-validation-error', { errors: this.errors });
                return;
            }
            
            this.loading = true;
            
            try {
                this.$dispatch('form-submit', { data: this.data });
                
                // 模拟 API 调用
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                this.$dispatch('form-success', { data: this.data });
            } catch (error) {
                this.$dispatch('form-error', { error });
            } finally {
                this.loading = false;
            }
        },
        
        // 重置表单
        reset() {
            this.fields.forEach(field => {
                this.data[field.name] = field.defaultValue || '';
            });
            this.errors = {};
        }
    };
}
```

### 3. 状态管理

#### 全局状态定义
```javascript
// assets/js/stores/app.js
document.addEventListener('alpine:init', () => {
    Alpine.store('app', {
        // 应用状态
        initialized: false,
        loading: false,
        error: null,
        
        // 用户状态
        user: null,
        authenticated: false,
        
        // UI 状态
        theme: 'light',
        sidebarOpen: false,
        notifications: [],
        
        // 初始化
        init() {
            this.loadFromStorage();
            this.initialized = true;
        },
        
        // 用户管理
        setUser(user) {
            this.user = user;
            this.authenticated = !!user;
            this.saveToStorage();
        },
        
        logout() {
            this.user = null;
            this.authenticated = false;
            this.clearStorage();
        },
        
        // 主题管理
        setTheme(theme) {
            this.theme = theme;
            document.documentElement.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
        },
        
        toggleTheme() {
            const newTheme = this.theme === 'light' ? 'dark' : 'light';
            this.setTheme(newTheme);
        },
        
        // 通知管理
        addNotification(notification) {
            const id = Date.now();
            this.notifications.push({
                id,
                type: 'info',
                duration: 3000,
                ...notification
            });
            
            // 自动移除
            if (notification.duration > 0) {
                setTimeout(() => {
                    this.removeNotification(id);
                }, notification.duration);
            }
        },
        
        removeNotification(id) {
            this.notifications = this.notifications.filter(n => n.id !== id);
        },
        
        // 存储管理
        loadFromStorage() {
            const user = localStorage.getItem('user');
            if (user) {
                this.setUser(JSON.parse(user));
            }
            
            const theme = localStorage.getItem('theme');
            if (theme) {
                this.setTheme(theme);
            }
        },
        
        saveToStorage() {
            if (this.user) {
                localStorage.setItem('user', JSON.stringify(this.user));
            }
        },
        
        clearStorage() {
            localStorage.removeItem('user');
        }
    });
});
```

#### 状态使用
```html
<!-- 在组件中使用全局状态 -->
<div x-data="{ 
    get user() { return $store.app.user; },
    get authenticated() { return $store.app.authenticated; }
}">
    <div x-show="authenticated">
        <span x-text="user?.username"></span>
        <button @click="$store.app.logout()">退出登录</button>
    </div>
    
    <div x-show="!authenticated">
        <a href="/login">登录</a>
    </div>
</div>
```

## 工具函数开发

### 1. API 工具
```javascript
// assets/js/utils/api.js
class ApiClient {
    constructor(baseURL = '/api') {
        this.baseURL = baseURL;
        this.defaultHeaders = {
            'Content-Type': 'application/json'
        };
    }
    
    // 设置认证令牌
    setAuthToken(token) {
        if (token) {
            this.defaultHeaders['Authorization'] = `Bearer ${token}`;
        } else {
            delete this.defaultHeaders['Authorization'];
        }
    }
    
    // 通用请求方法
    async request(method, url, data = null, options = {}) {
        const config = {
            method,
            headers: { ...this.defaultHeaders, ...options.headers },
            ...options
        };
        
        if (data) {
            config.body = JSON.stringify(data);
        }
        
        try {
            const response = await fetch(`${this.baseURL}${url}`, config);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            }
            
            return await response.text();
        } catch (error) {
            console.error('API Request failed:', error);
            throw error;
        }
    }
    
    // HTTP 方法
    get(url, options) {
        return this.request('GET', url, null, options);
    }
    
    post(url, data, options) {
        return this.request('POST', url, data, options);
    }
    
    put(url, data, options) {
        return this.request('PUT', url, data, options);
    }
    
    delete(url, options) {
        return this.request('DELETE', url, null, options);
    }
}

// 创建全局 API 实例
const api = new ApiClient();

// 导出
if (typeof window !== 'undefined') {
    window.api = api;
}
```

### 2. 验证工具
```javascript
// assets/js/utils/validator.js
const validators = {
    required: (value, message = '此字段为必填项') => {
        return !!value || message;
    },
    
    email: (value, message = '请输入有效的邮箱地址') => {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return !value || regex.test(value) || message;
    },
    
    minLength: (min, message) => (value) => {
        message = message || `最少${min}个字符`;
        return !value || value.length >= min || message;
    },
    
    maxLength: (max, message) => (value) => {
        message = message || `最多${max}个字符`;
        return !value || value.length <= max || message;
    },
    
    pattern: (regex, message = '格式不正确') => (value) => {
        return !value || regex.test(value) || message;
    },
    
    numeric: (value, message = '请输入数字') => {
        return !value || !isNaN(value) || message;
    },
    
    url: (value, message = '请输入有效的URL') => {
        try {
            return !value || new URL(value) || message;
        } catch {
            return message;
        }
    }
};

// 验证函数
function validate(value, rules) {
    for (const rule of rules) {
        const result = typeof rule === 'function' ? rule(value) : rule;
        if (result !== true) {
            return result;
        }
    }
    return true;
}

// 导出
if (typeof window !== 'undefined') {
    window.validators = validators;
    window.validate = validate;
}
```

## 样式开发

### 1. 自定义主题
```css
/* assets/css/themes/custom.css */
:root {
    --primary: #3b82f6;
    --primary-content: #ffffff;
    --secondary: #64748b;
    --secondary-content: #ffffff;
    --accent: #f59e0b;
    --accent-content: #ffffff;
    --neutral: #374151;
    --neutral-content: #ffffff;
    --base-100: #ffffff;
    --base-200: #f8fafc;
    --base-300: #e2e8f0;
    --base-content: #1f2937;
}

[data-theme="dark"] {
    --primary: #60a5fa;
    --primary-content: #1e3a8a;
    --secondary: #94a3b8;
    --secondary-content: #1e293b;
    --accent: #fbbf24;
    --accent-content: #92400e;
    --neutral: #d1d5db;
    --neutral-content: #111827;
    --base-100: #1f2937;
    --base-200: #374151;
    --base-300: #4b5563;
    --base-content: #f9fafb;
}
```

### 2. 组件样式
```css
/* assets/css/components.css */
.btn-gradient {
    @apply bg-gradient-to-r from-primary to-secondary;
    @apply text-primary-content;
    @apply border-none;
}

.card-hover {
    @apply transition-all duration-300;
    @apply hover:shadow-lg hover:-translate-y-1;
}

.form-floating {
    @apply relative;
}

.form-floating input {
    @apply pt-6 pb-2;
}

.form-floating label {
    @apply absolute left-3 top-2 text-sm text-base-content/60;
    @apply transition-all duration-200;
    @apply pointer-events-none;
}

.form-floating input:focus + label,
.form-floating input:not(:placeholder-shown) + label {
    @apply text-xs top-1 text-primary;
}
```

## 测试开发

### 1. 组件测试
```javascript
// tests/unit/components/button.test.js
describe('Button Component', () => {
    let container;
    
    beforeEach(() => {
        container = document.createElement('div');
        document.body.appendChild(container);
    });
    
    afterEach(() => {
        document.body.removeChild(container);
    });
    
    test('renders with default props', () => {
        container.innerHTML = `
            <div x-data="buttonComponent()">
                <button :class="classes" x-text="text"></button>
            </div>
        `;
        
        Alpine.initTree(container);
        
        const button = container.querySelector('button');
        expect(button.textContent).toBe('Button');
        expect(button.classList.contains('btn')).toBe(true);
        expect(button.classList.contains('btn-primary')).toBe(true);
    });
    
    test('handles click events', () => {
        let clickData = null;
        
        container.innerHTML = `
            <div x-data="buttonComponent()" @button-click="clickData = $event.detail">
                <button @click="handleClick"></button>
            </div>
        `;
        
        const scope = Alpine.initTree(container);
        scope.clickData = null;
        
        const button = container.querySelector('button');
        button.click();
        
        expect(scope.clickData).toEqual({
            variant: 'primary',
            text: 'Button'
        });
    });
});
```

### 2. 集成测试
```javascript
// tests/integration/form-submission.test.js
describe('Form Submission', () => {
    test('submits form with valid data', async () => {
        const mockApi = jest.fn().mockResolvedValue({ success: true });
        window.api.post = mockApi;
        
        const container = document.createElement('div');
        container.innerHTML = `
            <div x-data="formComponent({
                fields: [
                    { name: 'email', rules: [{ required: true }] }
                ]
            })">
                <input x-model="data.email" type="email">
                <button @click="submit()">Submit</button>
            </div>
        `;
        
        Alpine.initTree(container);
        
        const input = container.querySelector('input');
        const button = container.querySelector('button');
        
        input.value = '<EMAIL>';
        input.dispatchEvent(new Event('input'));
        
        button.click();
        
        await new Promise(resolve => setTimeout(resolve, 100));
        
        expect(mockApi).toHaveBeenCalledWith('/submit', {
            email: '<EMAIL>'
        });
    });
});
```

## 部署指南

### 1. 生产构建
```javascript
// scripts/build.js
const fs = require('fs');
const path = require('path');

function minifyCSS(css) {
    return css
        .replace(/\/\*[\s\S]*?\*\//g, '')
        .replace(/\s+/g, ' ')
        .trim();
}

function minifyJS(js) {
    // 简单的 JS 压缩
    return js
        .replace(/\/\*[\s\S]*?\*\//g, '')
        .replace(/\/\/.*$/gm, '')
        .replace(/\s+/g, ' ')
        .trim();
}

function build() {
    const distDir = path.join(__dirname, '../dist');
    
    if (!fs.existsSync(distDir)) {
        fs.mkdirSync(distDir, { recursive: true });
    }
    
    // 复制 HTML 文件
    const htmlFiles = ['index.html'];
    htmlFiles.forEach(file => {
        fs.copyFileSync(
            path.join(__dirname, '../', file),
            path.join(distDir, file)
        );
    });
    
    // 压缩 CSS
    const cssDir = path.join(__dirname, '../assets/css');
    const distCssDir = path.join(distDir, 'assets/css');
    fs.mkdirSync(distCssDir, { recursive: true });
    
    fs.readdirSync(cssDir).forEach(file => {
        if (file.endsWith('.css')) {
            const css = fs.readFileSync(path.join(cssDir, file), 'utf8');
            const minified = minifyCSS(css);
            fs.writeFileSync(path.join(distCssDir, file), minified);
        }
    });
    
    console.log('Build completed!');
}

build();
```

### 2. 开发服务器
```javascript
// scripts/dev.js
const express = require('express');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// 静态文件服务
app.use(express.static(path.join(__dirname, '..')));

// SPA 路由支持
app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../index.html'));
});

app.listen(PORT, () => {
    console.log(`Development server running at http://localhost:${PORT}`);
});
```

这个开发指南涵盖了从项目初始化到部署的完整开发流程，包括组件开发、状态管理、工具函数、样式开发和测试等各个方面。开发者可以按照这个指南快速上手项目开发。
