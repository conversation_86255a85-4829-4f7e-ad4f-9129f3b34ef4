# DaisyUI + Alpine.js CDN 项目

一个基于 DaisyUI 和 Alpine.js 的现代化前端项目，使用 CDN 方式引入依赖，实现快速开发和部署。

## 🚀 项目特性

- **🎨 现代化 UI**: 基于 DaisyUI 和 Tailwind CSS 的美观界面
- **⚡ 轻量级**: 使用 Alpine.js 实现响应式交互，无需复杂构建工具
- **📦 CDN 引入**: 所有依赖通过 CDN 引入，开箱即用
- **🔧 模块化设计**: 组件化开发，易于维护和扩展
- **📱 响应式布局**: 完美适配各种屏幕尺寸
- **🌙 主题切换**: 支持多种主题和暗黑模式
- **🛡️ 类型安全**: 遵循最佳实践，代码质量保证

## 📋 技术栈

- **UI 框架**: [DaisyUI](https://daisyui.com/) - 基于 Tailwind CSS 的组件库
- **JavaScript 框架**: [Alpine.js](https://alpinejs.dev/) - 轻量级响应式框架
- **CSS 框架**: [Tailwind CSS](https://tailwindcss.com/) - 实用优先的 CSS 框架
- **部署方式**: CDN 引入，支持静态部署

## 🏗️ 项目结构

```
TYKJ/
├── index.html                  # 主页面入口
├── 设计文档.md                 # 项目设计文档
├── 项目结构规划.md             # 项目结构规划
├── README.md                   # 项目说明文档
├── assets/                     # 静态资源目录
│   ├── css/                    # 样式文件
│   ├── js/                     # JavaScript 文件
│   │   ├── components/         # Alpine.js 组件
│   │   ├── utils/              # 工具函数
│   │   ├── stores/             # 全局状态管理
│   │   └── plugins/            # 插件系统
│   └── images/                 # 图片资源
├── components/                 # HTML 组件模板
├── pages/                      # 页面文件
├── docs/                       # 文档目录
│   ├── 组件文档.md             # 组件使用文档
│   ├── API文档.md              # API 接口文档
│   ├── 开发指南.md             # 开发指南
│   └── 部署指南.md             # 部署指南
└── tests/                      # 测试文件
```

## 🚀 快速开始

### 环境要求

- 现代浏览器 (Chrome 90+, Firefox 88+, Safari 14+)
- 本地开发服务器 (可选)

### 安装和运行

1. **克隆项目**
```bash
git clone <repository-url>
cd TYKJ
```

2. **直接运行**
```bash
# 方式一：直接用浏览器打开
open index.html

# 方式二：使用本地服务器
python -m http.server 3000
# 或
npx serve .
```

3. **访问应用**
```
http://localhost:3000
```

### CDN 依赖

项目使用以下 CDN 资源：

```html
<!-- DaisyUI + Tailwind CSS -->
<link href="https://cdn.jsdelivr.net/npm/daisyui@4.4.24/dist/full.min.css" rel="stylesheet">
<script src="https://cdn.tailwindcss.com"></script>

<!-- Alpine.js -->
<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js"></script>
```

## 📖 文档

### 核心文档
- [设计文档](./设计文档.md) - 项目架构和设计理念
- [项目结构规划](./项目结构规划.md) - 详细的目录结构说明

### 开发文档
- [组件文档](./docs/组件文档.md) - 组件使用指南和 API
- [开发指南](./docs/开发指南.md) - 开发环境配置和最佳实践
- [API文档](./docs/API文档.md) - 后端接口文档

### 部署文档
- [部署指南](./docs/部署指南.md) - 各种部署方式的详细说明

## 🧩 核心组件

### 基础组件
- **Button**: 可配置的按钮组件
- **Input**: 表单输入组件，支持验证
- **Select**: 下拉选择组件
- **Modal**: 模态对话框组件
- **Table**: 数据表格组件，支持排序和分页

### 布局组件
- **Navbar**: 响应式导航栏
- **Sidebar**: 可折叠侧边栏
- **Footer**: 页脚组件
- **Breadcrumb**: 面包屑导航

### 反馈组件
- **Toast**: 消息提示组件
- **Loading**: 加载指示器
- **Alert**: 警告提示组件

## 🎨 主题系统

项目支持多种主题，包括：

- **Light**: 浅色主题（默认）
- **Dark**: 深色主题
- **Cupcake**: 粉色主题
- **Bumblebee**: 黄色主题
- **Emerald**: 绿色主题

### 主题切换

```javascript
// 切换主题
Alpine.store('app').setTheme('dark');

// 获取当前主题
const currentTheme = Alpine.store('app').theme;
```

## 🔧 开发指南

### 组件开发

```javascript
// 创建新组件
function myComponent(config = {}) {
    return {
        // 数据
        data: config.data || {},
        
        // 初始化
        init() {
            console.log('Component initialized');
        },
        
        // 方法
        handleClick() {
            this.$dispatch('my-event', this.data);
        }
    };
}
```

### 状态管理

```javascript
// 使用全局状态
Alpine.store('app', {
    user: null,
    setUser(user) {
        this.user = user;
    }
});

// 在组件中使用
<div x-data="{ get user() { return $store.app.user; } }">
    <span x-text="user?.name"></span>
</div>
```

### API 调用

```javascript
// 使用内置 API 工具
const response = await api.get('/users');
const user = await api.post('/users', userData);
```

## 🧪 测试

```bash
# 运行测试
npm test

# 运行特定测试
npm test -- --grep "Button"

# 生成覆盖率报告
npm run test:coverage
```

## 📦 构建和部署

### 构建生产版本

```bash
# 构建
npm run build

# 构建特定环境
npm run build:staging
npm run build:production
```

### 部署选项

- **静态托管**: Vercel, Netlify, GitHub Pages
- **传统服务器**: Apache, Nginx
- **容器化**: Docker, Kubernetes
- **CDN**: CloudFlare, AWS CloudFront

详细部署说明请参考 [部署指南](./docs/部署指南.md)。

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 代码规范

- 遵循 ESLint 配置
- 使用 Prettier 格式化代码
- 编写详细的注释
- 添加适当的测试

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [DaisyUI](https://daisyui.com/) - 优秀的 UI 组件库
- [Alpine.js](https://alpinejs.dev/) - 轻量级 JavaScript 框架
- [Tailwind CSS](https://tailwindcss.com/) - 实用优先的 CSS 框架

## 📞 联系方式

- 项目链接: [https://github.com/username/TYKJ](https://github.com/username/TYKJ)
- 问题反馈: [Issues](https://github.com/username/TYKJ/issues)
- 功能请求: [Feature Requests](https://github.com/username/TYKJ/discussions)

## 🗺️ 路线图

### v1.0.0 (当前版本)
- ✅ 基础组件库
- ✅ 主题系统
- ✅ 响应式布局
- ✅ 文档完善

### v1.1.0 (计划中)
- 🔄 多语言支持 (i18n)
- 🔄 PWA 支持
- 🔄 更多组件
- 🔄 性能优化

### v2.0.0 (未来版本)
- 📋 TypeScript 支持
- 📋 构建工具集成
- 📋 插件生态系统
- 📋 可视化编辑器

---

**⭐ 如果这个项目对你有帮助，请给它一个 Star！**
