// 认证模块 JavaScript
// 提供认证相关的工具函数和验证逻辑

// 认证工具函数
const AuthUtils = {
    
    // 验证邮箱格式
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },
    
    // 验证密码强度
    validatePassword(password) {
        const errors = [];
        
        if (password.length < 6) {
            errors.push('密码长度至少6位');
        }
        
        if (password.length > 20) {
            errors.push('密码长度不能超过20位');
        }
        
        if (!/[a-zA-Z]/.test(password)) {
            errors.push('密码必须包含字母');
        }
        
        if (!/[0-9]/.test(password)) {
            errors.push('密码必须包含数字');
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors,
            strength: this.getPasswordStrength(password)
        };
    },
    
    // 获取密码强度
    getPasswordStrength(password) {
        let score = 0;
        
        // 长度评分
        if (password.length >= 8) score += 1;
        if (password.length >= 12) score += 1;
        
        // 字符类型评分
        if (/[a-z]/.test(password)) score += 1;
        if (/[A-Z]/.test(password)) score += 1;
        if (/[0-9]/.test(password)) score += 1;
        if (/[^a-zA-Z0-9]/.test(password)) score += 1;
        
        if (score <= 2) return 'weak';
        if (score <= 4) return 'medium';
        return 'strong';
    },
    
    // 验证用户名
    validateUsername(username) {
        const errors = [];
        
        if (username.length < 3) {
            errors.push('用户名长度至少3位');
        }
        
        if (username.length > 20) {
            errors.push('用户名长度不能超过20位');
        }
        
        if (!/^[a-zA-Z0-9_]+$/.test(username)) {
            errors.push('用户名只能包含字母、数字和下划线');
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    },
    
    // 验证确认密码
    validateConfirmPassword(password, confirmPassword) {
        if (password !== confirmPassword) {
            return {
                isValid: false,
                errors: ['两次输入的密码不一致']
            };
        }
        
        return {
            isValid: true,
            errors: []
        };
    },
    
    // 生成随机头像 URL
    generateAvatarUrl(seed) {
        const styles = ['avataaars', 'personas', 'initials'];
        const style = styles[Math.floor(Math.random() * styles.length)];
        return `https://api.dicebear.com/7.x/${style}/svg?seed=${encodeURIComponent(seed)}`;
    },
    
    // 格式化用户显示名称
    formatDisplayName(user) {
        if (user.name) return user.name;
        if (user.username) return user.username;
        if (user.email) return user.email.split('@')[0];
        return '用户';
    },
    
    // 检查是否为有效的登录凭据
    isValidLoginCredential(credential) {
        // 可以是用户名或邮箱
        return credential.length >= 3 && (
            this.validateUsername(credential).isValid || 
            this.validateEmail(credential)
        );
    }
};

// 认证 API 模拟
const AuthAPI = {
    
    // 模拟登录 API
    async login(credentials) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                // 模拟验证逻辑
                const { username, password } = credentials;
                
                // 简单的模拟验证
                if ((username === 'admin' && password === 'admin123') ||
                    (username === '<EMAIL>' && password === 'user123') ||
                    (username === 'demo' && password === 'demo123')) {
                    
                    // 生成模拟用户数据
                    const userData = {
                        id: Math.floor(Math.random() * 1000) + 1,
                        username: username === '<EMAIL>' ? 'user' : username,
                        email: username.includes('@') ? username : `${username}@example.com`,
                        name: AuthUtils.formatDisplayName({ username }),
                        avatar: AuthUtils.generateAvatarUrl(username),
                        role: username === 'admin' ? 'admin' : 'user',
                        lastLoginAt: new Date().toISOString(),
                        createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString()
                    };
                    
                    resolve(userData);
                } else {
                    reject(new Error('用户名或密码错误'));
                }
            }, 1000 + Math.random() * 1000); // 模拟网络延迟
        });
    },
    
    // 模拟注册 API
    async register(userData) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                const { username, email, password } = userData;
                
                // 模拟用户名/邮箱已存在检查
                const existingUsers = ['admin', 'test', 'demo'];
                const existingEmails = ['<EMAIL>', '<EMAIL>'];
                
                if (existingUsers.includes(username)) {
                    reject(new Error('用户名已存在'));
                    return;
                }
                
                if (existingEmails.includes(email)) {
                    reject(new Error('邮箱已被注册'));
                    return;
                }
                
                // 生成新用户数据
                const newUser = {
                    id: Math.floor(Math.random() * 1000) + 1000,
                    username,
                    email,
                    name: username,
                    avatar: AuthUtils.generateAvatarUrl(username),
                    role: 'user',
                    createdAt: new Date().toISOString(),
                    lastLoginAt: null
                };
                
                resolve(newUser);
            }, 1500 + Math.random() * 1000);
        });
    },
    
    // 模拟忘记密码 API
    async forgotPassword(email) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                if (AuthUtils.validateEmail(email)) {
                    resolve({
                        message: '密码重置邮件已发送到您的邮箱',
                        email: email
                    });
                } else {
                    reject(new Error('请输入有效的邮箱地址'));
                }
            }, 1000);
        });
    },
    
    // 模拟重置密码 API
    async resetPassword(token, newPassword) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                // 模拟 token 验证
                if (token && token.length > 10) {
                    const passwordValidation = AuthUtils.validatePassword(newPassword);
                    if (passwordValidation.isValid) {
                        resolve({
                            message: '密码重置成功'
                        });
                    } else {
                        reject(new Error(passwordValidation.errors[0]));
                    }
                } else {
                    reject(new Error('无效的重置令牌'));
                }
            }, 1000);
        });
    },
    
    // 模拟验证邮箱 API
    async verifyEmail(token) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                if (token && token.length > 10) {
                    resolve({
                        message: '邮箱验证成功'
                    });
                } else {
                    reject(new Error('无效的验证令牌'));
                }
            }, 1000);
        });
    },
    
    // 模拟检查用户名可用性
    async checkUsernameAvailability(username) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const unavailableUsernames = ['admin', 'test', 'demo', 'user', 'root'];
                const isAvailable = !unavailableUsernames.includes(username.toLowerCase());
                resolve({
                    available: isAvailable,
                    username: username
                });
            }, 500);
        });
    },
    
    // 模拟检查邮箱可用性
    async checkEmailAvailability(email) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const unavailableEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
                const isAvailable = !unavailableEmails.includes(email.toLowerCase());
                resolve({
                    available: isAvailable,
                    email: email
                });
            }, 500);
        });
    }
};

// 表单验证器
const FormValidators = {
    
    // 登录表单验证
    validateLoginForm(formData) {
        const errors = {};
        
        // 验证用户名/邮箱
        if (!formData.username || !formData.username.trim()) {
            errors.username = '请输入用户名或邮箱';
        } else if (!AuthUtils.isValidLoginCredential(formData.username.trim())) {
            errors.username = '请输入有效的用户名或邮箱';
        }
        
        // 验证密码
        if (!formData.password || !formData.password.trim()) {
            errors.password = '请输入密码';
        } else if (formData.password.length < 6) {
            errors.password = '密码长度至少6位';
        }
        
        return {
            isValid: Object.keys(errors).length === 0,
            errors: errors
        };
    },
    
    // 注册表单验证
    validateRegisterForm(formData) {
        const errors = {};
        
        // 验证用户名
        if (!formData.username || !formData.username.trim()) {
            errors.username = '请输入用户名';
        } else {
            const usernameValidation = AuthUtils.validateUsername(formData.username.trim());
            if (!usernameValidation.isValid) {
                errors.username = usernameValidation.errors[0];
            }
        }
        
        // 验证邮箱
        if (!formData.email || !formData.email.trim()) {
            errors.email = '请输入邮箱地址';
        } else if (!AuthUtils.validateEmail(formData.email.trim())) {
            errors.email = '请输入有效的邮箱地址';
        }
        
        // 验证密码
        if (!formData.password || !formData.password.trim()) {
            errors.password = '请输入密码';
        } else {
            const passwordValidation = AuthUtils.validatePassword(formData.password);
            if (!passwordValidation.isValid) {
                errors.password = passwordValidation.errors[0];
            }
        }
        
        // 验证确认密码
        if (!formData.confirmPassword || !formData.confirmPassword.trim()) {
            errors.confirmPassword = '请确认密码';
        } else {
            const confirmValidation = AuthUtils.validateConfirmPassword(
                formData.password, 
                formData.confirmPassword
            );
            if (!confirmValidation.isValid) {
                errors.confirmPassword = confirmValidation.errors[0];
            }
        }
        
        // 验证服务条款
        if (!formData.agreeTerms) {
            errors.agreeTerms = '请同意服务条款';
        }
        
        return {
            isValid: Object.keys(errors).length === 0,
            errors: errors
        };
    },
    
    // 忘记密码表单验证
    validateForgotPasswordForm(formData) {
        const errors = {};
        
        if (!formData.email || !formData.email.trim()) {
            errors.email = '请输入邮箱地址';
        } else if (!AuthUtils.validateEmail(formData.email.trim())) {
            errors.email = '请输入有效的邮箱地址';
        }
        
        return {
            isValid: Object.keys(errors).length === 0,
            errors: errors
        };
    }
};

// 导出到全局作用域
if (typeof window !== 'undefined') {
    window.AuthUtils = AuthUtils;
    window.AuthAPI = AuthAPI;
    window.FormValidators = FormValidators;
}
